# Cloudflare Pages 部署指南

## 🚀 部署配置

### 1. Cloudflare Pages 设置

在 Cloudflare Pages 控制台中配置以下设置：

**构建设置：**
- 构建命令：`npm run build`
- 构建输出目录：`out`
- Node.js 版本：`18` 或更高

**环境变量：**
```
NODE_VERSION=18
NPM_CONFIG_PRODUCTION=false
```

### 2. 自动部署配置

**GitHub 集成：**
1. 连接 GitHub 仓库
2. 选择生产分支（通常是 `main` 或 `master`）
3. 启用自动部署

**构建触发器：**
- 推送到主分支时自动构建
- Pull Request 预览部署

### 3. 性能优化

**缓存策略：**
- 静态资源缓存 1 年
- 图片缓存 1 个月
- HTML 文件不缓存（实时更新）

**压缩：**
- 启用 Gzip/Brotli 压缩
- 自动 CSS/JS 压缩
- 图片优化（WebP 转换）

### 4. 安全配置

**Headers 配置：**
- CSP（内容安全策略）
- HSTS（HTTP 严格传输安全）
- X-Frame-Options
- X-Content-Type-Options

**域名配置：**
- 自定义域名：`sybaumeme.com`
- SSL/TLS 加密：Full (strict)
- Always Use HTTPS：启用

### 5. 监控和分析

**Cloudflare Analytics：**
- 页面访问统计
- 性能指标监控
- 错误率追踪

**Google Analytics：**
- 已集成 GA4
- 自动事件追踪
- 转化率分析

## 🛠️ 本地测试

### 构建静态版本
```bash
npm run build
```

### 预览构建结果
```bash
npx serve out
```

### Cloudflare 特定构建
```bash
npm run build:cloudflare
```

## 📊 性能优化建议

### 1. 图片优化
- 使用 WebP 格式
- 实现懒加载
- 响应式图片

### 2. 代码分割
- 动态导入组件
- 路由级别分割
- 第三方库分离

### 3. 缓存策略
- 静态资源长期缓存
- API 响应缓存
- 浏览器缓存优化

## 🔧 故障排除

### 常见问题

**构建失败：**
1. 检查 Node.js 版本
2. 清除 npm 缓存
3. 检查依赖版本兼容性

**路由问题：**
1. 确认 `_redirects` 文件配置
2. 检查动态路由设置
3. 验证 404 页面处理

**性能问题：**
1. 分析包大小
2. 检查未使用的依赖
3. 优化图片和字体

## 📈 部署检查清单

- [ ] 构建成功无错误
- [ ] 所有路由正常工作
- [ ] 图片和静态资源加载正常
- [ ] SEO 元数据正确
- [ ] 性能指标达标（LCP < 2.5s）
- [ ] 安全头部配置正确
- [ ] 分析工具正常工作
- [ ] 移动端适配良好
- [ ] 浏览器兼容性测试通过

## 🌐 域名和 DNS 配置

### DNS 记录
```
Type: CNAME
Name: @
Target: sybaumeme.pages.dev

Type: CNAME  
Name: www
Target: sybaumeme.pages.dev
```

### SSL 证书
- 自动 SSL 证书
- Edge 证书（免费）
- 通配符证书支持

## 📞 支持和维护

### 监控
- Uptime 监控
- 性能监控
- 错误日志监控

### 备份
- 代码仓库备份
- 配置文件备份
- 数据库备份（如适用）

### 更新流程
1. 开发环境测试
2. 预览部署验证
3. 生产环境发布
4. 监控和回滚准备
