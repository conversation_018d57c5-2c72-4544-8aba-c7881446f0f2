# Cloudflare Pages Headers Configuration

# Cache static assets for 1 year
/_next/static/*
  Cache-Control: public, max-age=31536000, immutable

# Cache images for 1 month
/images/*
  Cache-Control: public, max-age=2592000

# Cache fonts for 1 year
*.woff2
  Cache-Control: public, max-age=31536000, immutable

*.woff
  Cache-Control: public, max-age=31536000, immutable

# Security headers for all pages
/*
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: camera=(), microphone=(), geolocation=()

# CSP for enhanced security
/*
  Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.googletagmanager.com https://pagead2.googlesyndication.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: blob: https:; connect-src 'self' https://www.google-analytics.com https://vitals.vercel-analytics.com; frame-src https://pagead2.googlesyndication.com;
