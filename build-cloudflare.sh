#!/bin/bash

# Cloudflare Pages Build Script for Sybau Meme Generator

echo "🚀 Starting Cloudflare Pages build..."

# Set Node.js version
export NODE_VERSION="18"

# Install dependencies
echo "📦 Installing dependencies..."
npm ci

# Build the application
echo "🔨 Building application..."
npm run build

# Copy static files to out directory
echo "📁 Copying static files..."
cp _headers out/_headers 2>/dev/null || echo "No _headers file found"
cp _redirects out/_redirects 2>/dev/null || echo "No _redirects file found"

# Create robots.txt if it doesn't exist
if [ ! -f "out/robots.txt" ]; then
  echo "🤖 Creating robots.txt..."
  cat > out/robots.txt << EOF
User-agent: *
Allow: /

Sitemap: https://sybaumeme.com/sitemap.xml
EOF
fi

echo "✅ Build completed successfully!"
echo "📊 Build statistics:"
du -sh out/
echo "📁 Files in out directory:"
ls -la out/ | head -20
