/** @type {import('next').NextConfig} */
const nextConfig = {
  // 开发环境使用正常模式，生产环境使用静态导出
  output: process.env.NODE_ENV === 'production' ? 'export' : undefined,

  // 禁用图片优化，因为静态导出不支持
  images: {
    unoptimized: true,
  },

  // 启用压缩
  compress: true,

  // 启用实验性功能
  experimental: {
    // 优化包大小
    optimizePackageImports: ['lucide-react', 'framer-motion'],
  },

  // 配置路径
  trailingSlash: false,

  // ESLint配置
  eslint: {
    // 在生产构建时忽略ESLint错误
    ignoreDuringBuilds: true,
  },
  
  // Webpack配置优化
  webpack: (config, { isServer }) => {
    // 优化包大小
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
      };
    }
    
    return config;
  },
};

export default nextConfig;
