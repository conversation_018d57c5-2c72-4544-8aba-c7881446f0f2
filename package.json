{"name": "meme-generator-next", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "build:cloudflare": "./build-cloudflare.sh", "start": "next start", "lint": "next lint", "export": "next build && next export"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-select": "^2.2.5", "@vercel/analytics": "^1.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.12.1", "lucide-react": "^0.511.0", "next": "15.3.2", "next-themes": "^0.4.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-share": "^5.2.2", "sharp": "^0.34.3", "sonner": "^2.0.3", "tailwind-merge": "^3.3.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.3.2", "lightningcss": "^1.30.1", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "tw-animate-css": "^1.3.0", "typescript": "^5"}}