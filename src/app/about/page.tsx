import { Metada<PERSON> } from 'next';
import { siteConfig } from '@/data/site-config';
import { Heart, Users, Zap, Sparkles, Target, Globe } from 'lucide-react';

export const metadata: Metadata = {
  title: 'About Us - Sybau Meme Generator',
  description: 'Learn about Sybau Meme Generator - the ultimate platform for creating viral sybau memes. Discover our mission, values, and commitment to creators.',
  openGraph: {
    title: 'About Us - Sybau Meme Generator',
    description: 'Learn about Sybau Meme Generator - the ultimate platform for creating viral sybau memes. Discover our mission, values, and commitment to creators.',
    url: `${siteConfig.url}/about`,
    siteName: siteConfig.name,
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'About Us - Sybau Meme Generator',
    description: 'Learn about Sybau Meme Generator - the ultimate platform for creating viral sybau memes.',
  },
  alternates: {
    canonical: `${siteConfig.url}/about`,
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default function AboutPage() {
  return (
    <div className="max-w-6xl mx-auto px-4 py-8">
      {/* Hero Section */}
      <div className="text-center mb-16">
        <h1 className="text-5xl font-bold mb-6">
          About <span className="sybau-text-gradient">Sybau</span> Meme Generator
        </h1>
        <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
          We're passionate about empowering creators through the universal language of memes.
          Our platform provides the tools for users to express themselves, share laughs, and create viral content.
        </p>
      </div>

      {/* Mission Section */}
      <div className="grid md:grid-cols-2 gap-12 mb-16">
        <div>
          <div className="flex items-center gap-3 mb-6">
            <Target className="w-8 h-8 text-primary" />
            <h2 className="text-3xl font-bold">Our Mission</h2>
          </div>
          <p className="text-lg text-muted-foreground leading-relaxed mb-4">
            To democratize meme creation and make it accessible to everyone. We believe that humor
            and creativity should have no barriers, which is why our platform is completely free and easy to use.
          </p>
          <p className="text-lg text-muted-foreground leading-relaxed">
            Whether you're a seasoned meme creator or just starting your journey, Sybau Meme Generator provides the tools 
            and templates you need to bring your ideas to life and share them with the world.
          </p>
        </div>
        
        <div>
          <div className="flex items-center gap-3 mb-6">
            <Heart className="w-8 h-8 text-primary" />
            <h2 className="text-3xl font-bold">Our Values</h2>
          </div>
          <div className="space-y-4">
            <div className="flex items-start gap-3">
              <Sparkles className="w-5 h-5 text-primary mt-1" />
              <div>
                <h3 className="font-semibold mb-1">Creativity First</h3>
                <p className="text-muted-foreground">We celebrate original thinking and unique perspectives in meme creation.</p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <Users className="w-5 h-5 text-primary mt-1" />
              <div>
                <h3 className="font-semibold mb-1">User Focused</h3>
                <p className="text-muted-foreground">Our platform is built with user feedback and needs in mind.</p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <Zap className="w-5 h-5 text-primary mt-1" />
              <div>
                <h3 className="font-semibold mb-1">Always Free</h3>
                <p className="text-muted-foreground">Quality meme creation tools should be accessible to everyone.</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Story Section */}
      <div className="bg-accent/20 rounded-2xl p-8 mb-16">
        <div className="flex items-center gap-3 mb-6">
          <Globe className="w-8 h-8 text-primary" />
          <h2 className="text-3xl font-bold">Our Story</h2>
        </div>
        <div className="grid md:grid-cols-2 gap-8">
          <div>
            <h3 className="text-xl font-semibold mb-4">The Beginning</h3>
            <p className="text-muted-foreground leading-relaxed mb-4">
              Sybau Meme Generator was born from a simple observation: the sybau community needed a dedicated platform 
              for creating and sharing memes that truly resonated with their culture and humor.
            </p>
            <p className="text-muted-foreground leading-relaxed">
              What started as a small project to serve the sybau community has grown into a comprehensive meme creation 
              platform that celebrates creativity, humor, and community connection.
            </p>
          </div>
          <div>
            <h3 className="text-xl font-semibold mb-4">Today & Beyond</h3>
            <p className="text-muted-foreground leading-relaxed mb-4">
              Today, thousands of users create viral sybau memes using our platform every day. We've built an extensive 
              library of templates, tools, and features that make meme creation both fun and accessible.
            </p>
            <p className="text-muted-foreground leading-relaxed">
              We're constantly evolving, adding new templates, improving our tools, and listening to our community 
              to ensure we're meeting the needs of meme creators everywhere.
            </p>
          </div>
        </div>
      </div>

      {/* Features Highlight */}
      <div className="mb-16">
        <h2 className="text-3xl font-bold text-center mb-12">What Makes Us Special</h2>
        <div className="grid md:grid-cols-3 gap-8">
          <div className="text-center p-6 rounded-xl bg-accent/10">
            <div className="w-16 h-16 bg-primary/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <Sparkles className="w-8 h-8 text-primary" />
            </div>
            <h3 className="text-xl font-semibold mb-3">Popular Templates</h3>
            <p className="text-muted-foreground">
              Curated collection of popular meme templates based on current internet trends and viral formats.
            </p>
          </div>
          
          <div className="text-center p-6 rounded-xl bg-accent/10">
            <div className="w-16 h-16 bg-primary/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <Zap className="w-8 h-8 text-primary" />
            </div>
            <h3 className="text-xl font-semibold mb-3">Lightning Fast</h3>
            <p className="text-muted-foreground">
              Create and download high-quality memes in seconds with our optimized, user-friendly interface.
            </p>
          </div>
          
          <div className="text-center p-6 rounded-xl bg-accent/10">
            <div className="w-16 h-16 bg-primary/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <Users className="w-8 h-8 text-primary" />
            </div>
            <h3 className="text-xl font-semibold mb-3">User Friendly</h3>
            <p className="text-muted-foreground">
              Built with intuitive features and templates that make meme creation accessible to everyone.
            </p>
          </div>
        </div>
      </div>

      {/* Statistics */}
      <div className="bg-primary/10 rounded-2xl p-8 mb-16">
        <h2 className="text-3xl font-bold text-center mb-8">Our Impact</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
          <div>
            <div className="text-3xl font-bold text-primary mb-2">50K+</div>
            <div className="text-muted-foreground">Memes Created</div>
          </div>
          <div>
            <div className="text-3xl font-bold text-primary mb-2">100+</div>
            <div className="text-muted-foreground">Templates Available</div>
          </div>
          <div>
            <div className="text-3xl font-bold text-primary mb-2">10K+</div>
            <div className="text-muted-foreground">Active Users</div>
          </div>
          <div>
            <div className="text-3xl font-bold text-primary mb-2">24/7</div>
            <div className="text-muted-foreground">Always Available</div>
          </div>
        </div>
      </div>

      {/* Team Philosophy */}
      <div className="text-center mb-16">
        <h2 className="text-3xl font-bold mb-6">Our Philosophy</h2>
        <div className="max-w-4xl mx-auto">
          <p className="text-lg text-muted-foreground leading-relaxed mb-6">
            We believe that memes are more than just entertainment—they're a form of digital art, social commentary, 
            and community building. Every meme tells a story, shares an emotion, or brings people together through shared humor.
          </p>
          <p className="text-lg text-muted-foreground leading-relaxed">
            Our commitment is to provide the best tools, templates, and experience for creators while maintaining 
            the authentic spirit of the sybau community. We're not just building a platform; we're nurturing a creative ecosystem.
          </p>
        </div>
      </div>

      {/* Call to Action */}
      <div className="text-center bg-gradient-to-r from-primary/20 to-primary/10 rounded-2xl p-12">
        <h2 className="text-3xl font-bold mb-4">Start Creating Today</h2>
        <p className="text-lg text-muted-foreground mb-8 max-w-2xl mx-auto">
          Ready to create your next viral sybau meme? Join thousands of users who trust Sybau Meme Generator
          for their meme creation needs.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <a
            href="/"
            className="inline-flex items-center gap-2 bg-primary text-primary-foreground px-8 py-4 rounded-lg font-semibold hover:bg-primary/90 transition-colors"
          >
            <Sparkles className="w-5 h-5" />
            Start Creating Memes
          </a>
          <a
            href="/contact"
            className="inline-flex items-center gap-2 border border-border px-8 py-4 rounded-lg font-semibold hover:bg-accent transition-colors"
          >
            <Heart className="w-5 h-5" />
            Get in Touch
          </a>
        </div>
      </div>
    </div>
  );
}
