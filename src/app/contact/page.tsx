import { Metadata } from 'next';
import { siteConfig } from '@/data/site-config';
import { Mail, MessageCircle, HelpCircle, Shield, Globe } from 'lucide-react';

export const metadata: Metadata = {
  title: 'Contact Us - Sybau Meme Generator',
  description: 'Get in touch with the Sybau Meme Generator team. We are here to help with questions, feedback, and support for our meme creation platform.',
  openGraph: {
    title: 'Contact Us - Sybau Meme Generator',
    url: `${siteConfig.url}/contact`,
    siteName: siteConfig.name,
    type: 'website',
  },
  alternates: {
    canonical: `${siteConfig.url}/contact`,
  },
};

export default function ContactPage() {
  return (
    <div className="max-w-6xl mx-auto px-4 py-8">
      <div className="text-center mb-16">
        <h1 className="text-5xl font-bold mb-6">Contact Us</h1>
        <p className="text-xl text-muted-foreground">Get in touch with our team.</p>
      </div>
      <div className="grid md:grid-cols-3 gap-8">
        <div className="bg-accent/10 p-8 rounded-2xl text-center">
          <Mail className="w-12 h-12 text-primary mx-auto mb-4" />
          <h3 className="text-xl font-semibold mb-3">General Inquiries</h3>
          <a href="mailto:<EMAIL>" className="text-primary hover:underline">
            <EMAIL>
          </a>
        </div>
        <div className="bg-accent/10 p-8 rounded-2xl text-center">
          <HelpCircle className="w-12 h-12 text-primary mx-auto mb-4" />
          <h3 className="text-xl font-semibold mb-3">Support</h3>
          <a href="mailto:<EMAIL>" className="text-primary hover:underline">
            <EMAIL>
          </a>
        </div>
        <div className="bg-accent/10 p-8 rounded-2xl text-center">
          <Shield className="w-12 h-12 text-primary mx-auto mb-4" />
          <h3 className="text-xl font-semibold mb-3">Legal</h3>
          <a href="mailto:<EMAIL>" className="text-primary hover:underline">
            <EMAIL>
          </a>
        </div>
      </div>
    </div>
  );
}
