@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

/* Font faces for meme editor fonts */
@font-face {
  font-family: 'Impact';
  font-display: swap;
  src: local('Impact'), local('Impact-Regular');
}

@font-face {
  font-family: 'Arial Black';
  font-display: swap;
  src: local('Arial Black'), local('ArialBlack');
}

@font-face {
  font-family: 'Helvetica Neue';
  font-display: swap;
  src: local('Helvetica Neue'), local('HelveticaNeue');
}

@font-face {
  font-family: 'Roboto Condensed';
  font-display: swap;
  src: local('Roboto Condensed'), local('RobotoCondensed-Regular');
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.488 0.243 264.376); /* Sybau brand purple-blue */
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.95 0.05 264.376); /* Light sybau accent */
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.488 0.243 264.376); /* Sybau brand ring */
  --chart-1: oklch(0.488 0.243 264.376); /* Sybau primary */
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376); /* Sybau brand */
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.488 0.243 264.376); /* Sybau brand ring */
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    border-color: var(--color-border);
  }

  body {
    background-color: var(--color-background);
    color: var(--color-foreground);
  }
}

/* Improve font rendering for canvas */
canvas {
  font-smooth: always;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Ensure font families are available globally */
.font-impact {
  font-family: 'Impact', 'Arial Black', 'Helvetica Neue', 'Arial', sans-serif !important;
}

.font-anton {
  font-family: 'Anton', 'Impact', 'Arial Black', sans-serif !important;
}

.font-oswald {
  font-family: 'Oswald', 'Impact', 'Arial Black', sans-serif !important;
}

.font-bebas {
  font-family: 'Bebas Neue', 'Impact', 'Arial Black', sans-serif !important;
}

.font-arial-black {
  font-family: 'Arial Black', 'Impact', 'Arial', sans-serif !important;
}

.font-helvetica {
  font-family: 'Helvetica Neue', 'Arial', sans-serif !important;
}

.font-roboto-condensed {
  font-family: 'Roboto Condensed', 'Arial Black', 'Arial', sans-serif !important;
}

.font-montserrat {
  font-family: 'Montserrat', 'Arial', sans-serif !important;
}

.font-open-sans {
  font-family: 'Open Sans', 'Arial', sans-serif !important;
}

.font-lato {
  font-family: 'Lato', 'Arial', sans-serif !important;
}

.font-poppins {
  font-family: 'Poppins', 'Arial', sans-serif !important;
}

.font-source-sans {
  font-family: 'Source Sans Pro', 'Arial', sans-serif !important;
}

.font-nunito {
  font-family: 'Nunito', 'Arial', sans-serif !important;
}

.font-inter {
  font-family: 'Inter', 'Arial', sans-serif !important;
}

.font-work-sans {
  font-family: 'Work Sans', 'Arial', sans-serif !important;
}

/* Sybau brand specific styles */
.sybau-gradient {
  background: linear-gradient(135deg, #6a7bd1 0%, #8a9cf1 50%, #6a7bd1 100%);
}

.sybau-text-gradient {
  background: linear-gradient(135deg, #6a7bd1 0%, #8a9cf1 50%, #6a7bd1 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.sybau-glow {
  box-shadow: 0 0 20px rgba(106, 123, 209, 0.3);
}

.sybau-border {
  border: 2px solid transparent;
  background: linear-gradient(white, white) padding-box,
              linear-gradient(135deg, #6a7bd1, #8a9cf1) border-box;
}

/* Enhanced gradient animation */
@keyframes gradient-flow {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.animate-gradient-flow {
  animation: gradient-flow 3s ease infinite;
  background-size: 200% 200%;
}