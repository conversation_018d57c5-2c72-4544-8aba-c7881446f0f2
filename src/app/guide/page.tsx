import { Metadata } from 'next';
import { siteConfig } from '@/data/site-config';
import { BookOpen, Zap, Upload, Download, Palette, Type, Smartphone, Share2, Lightbulb, Target } from 'lucide-react';
import RelatedPages from '@/components/RelatedPages';

export const metadata: Metadata = {
  title: 'Complete Guide to Creating Viral Sybau Memes - Sybau Meme Generator',
  description: 'Master the art of sybau meme creation with our comprehensive guide. Learn techniques, tips, and best practices for creating viral memes.',
  openGraph: {
    title: 'Complete Guide to Creating Viral Sybau Memes',
    description: 'Master the art of sybau meme creation with our comprehensive guide. Learn techniques, tips, and best practices for creating viral memes.',
    url: `${siteConfig.url}/guide`,
    siteName: siteConfig.name,
    type: 'article',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Complete Guide to Creating Viral Sybau Memes',
    description: 'Master the art of sybau meme creation with our comprehensive guide.',
  },
  alternates: {
    canonical: `${siteConfig.url}/guide`,
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default function GuidePage() {
  return (
    <div className="max-w-4xl mx-auto px-4 py-8">
      <div className="prose prose-lg dark:prose-invert max-w-none">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <div className="flex items-center justify-center gap-3 mb-6">
            <BookOpen className="w-12 h-12 text-primary" />
            <h1 className="text-5xl font-bold mb-0">
              The Ultimate <span className="sybau-text-gradient">Sybau</span> Meme Guide
            </h1>
          </div>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            Master the art of creating viral sybau memes with our comprehensive guide. 
            From basic techniques to advanced strategies, learn everything you need to become a meme creation expert.
          </p>
        </div>

        {/* Table of Contents */}
        <div className="bg-accent/20 rounded-2xl p-8 mb-12">
          <h2 className="text-2xl font-bold mb-6">What You'll Learn</h2>
          <div className="grid md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <a href="#getting-started" className="block text-primary hover:underline">1. Getting Started with Sybau Memes</a>
              <a href="#choosing-templates" className="block text-primary hover:underline">2. Choosing the Perfect Template</a>
              <a href="#text-techniques" className="block text-primary hover:underline">3. Text Placement and Typography</a>
              <a href="#design-principles" className="block text-primary hover:underline">4. Design Principles for Viral Memes</a>
              <a href="#sybau-culture" className="block text-primary hover:underline">5. Understanding Sybau Culture</a>
            </div>
            <div className="space-y-2">
              <a href="#advanced-techniques" className="block text-primary hover:underline">6. Advanced Creation Techniques</a>
              <a href="#mobile-optimization" className="block text-primary hover:underline">7. Mobile-First Meme Design</a>
              <a href="#sharing-strategies" className="block text-primary hover:underline">8. Sharing and Distribution</a>
              <a href="#common-mistakes" className="block text-primary hover:underline">9. Common Mistakes to Avoid</a>
              <a href="#pro-tips" className="block text-primary hover:underline">10. Pro Tips for Viral Success</a>
            </div>
          </div>
        </div>

        {/* Section 1: Getting Started */}
        <section id="getting-started" className="mb-12">
          <div className="flex items-center gap-3 mb-6">
            <Zap className="w-8 h-8 text-primary" />
            <h2 className="text-3xl font-bold mb-0">1. Getting Started with Sybau Memes</h2>
          </div>
          
          <p className="text-lg mb-6">
            Creating your first sybau meme is easier than you think! Our platform is designed to make meme creation 
            accessible to everyone, regardless of design experience.
          </p>

          <h3 className="text-xl font-semibold mb-4">Step-by-Step Process:</h3>
          <div className="space-y-4 mb-8">
            <div className="flex items-start gap-4 p-4 bg-accent/10 rounded-lg">
              <div className="w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center font-bold text-sm">1</div>
              <div>
                <h4 className="font-semibold mb-2">Browse Templates</h4>
                <p className="text-muted-foreground">Start by exploring our extensive collection of sybau meme templates. Each template is carefully curated based on popular internet trends.</p>
              </div>
            </div>
            
            <div className="flex items-start gap-4 p-4 bg-accent/10 rounded-lg">
              <div className="w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center font-bold text-sm">2</div>
              <div>
                <h4 className="font-semibold mb-2">Select Your Template</h4>
                <p className="text-muted-foreground">Click on any template that catches your eye. Consider the emotion, context, and potential for your message.</p>
              </div>
            </div>
            
            <div className="flex items-start gap-4 p-4 bg-accent/10 rounded-lg">
              <div className="w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center font-bold text-sm">3</div>
              <div>
                <h4 className="font-semibold mb-2">Add Your Text</h4>
                <p className="text-muted-foreground">Use our intuitive editor to add text. Most memes work best with concise, punchy messages that are easy to read.</p>
              </div>
            </div>
            
            <div className="flex items-start gap-4 p-4 bg-accent/10 rounded-lg">
              <div className="w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center font-bold text-sm">4</div>
              <div>
                <h4 className="font-semibold mb-2">Customize and Download</h4>
                <p className="text-muted-foreground">Adjust text positioning, font size, and styling. When you're happy with your creation, download it in high quality.</p>
              </div>
            </div>
          </div>
        </section>

        {/* Section 2: Choosing Templates */}
        <section id="choosing-templates" className="mb-12">
          <div className="flex items-center gap-3 mb-6">
            <Target className="w-8 h-8 text-primary" />
            <h2 className="text-3xl font-bold mb-0">2. Choosing the Perfect Template</h2>
          </div>
          
          <p className="text-lg mb-6">
            The template you choose sets the tone for your entire meme. Here's how to select templates that will 
            maximize your meme's impact and shareability.
          </p>

          <h3 className="text-xl font-semibold mb-4">Template Categories:</h3>
          <div className="grid md:grid-cols-2 gap-6 mb-8">
            <div className="p-6 bg-accent/10 rounded-lg">
              <h4 className="font-semibold mb-3">🔥 Trending Templates</h4>
              <p className="text-muted-foreground mb-3">
                These are currently popular on the internet. Using trending templates increases your chances of viral success.
              </p>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• High engagement potential</li>
                <li>• Community recognition</li>
                <li>• Current relevance</li>
              </ul>
            </div>
            
            <div className="p-6 bg-accent/10 rounded-lg">
              <h4 className="font-semibold mb-3">⚡ Classic Templates</h4>
              <p className="text-muted-foreground mb-3">
                Timeless formats that never go out of style. Perfect for universal messages and broad appeal.
              </p>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Universal recognition</li>
                <li>• Versatile messaging</li>
                <li>• Proven effectiveness</li>
              </ul>
            </div>
          </div>

          <h3 className="text-xl font-semibold mb-4">Selection Criteria:</h3>
          <ul className="space-y-3 mb-6">
            <li className="flex items-start gap-3">
              <div className="w-2 h-2 bg-primary rounded-full mt-2"></div>
              <div>
                <strong>Emotional Resonance:</strong> Choose templates that match the emotion you want to convey
              </div>
            </li>
            <li className="flex items-start gap-3">
              <div className="w-2 h-2 bg-primary rounded-full mt-2"></div>
              <div>
                <strong>Visual Clarity:</strong> Ensure the template has clear areas for text placement
              </div>
            </li>
            <li className="flex items-start gap-3">
              <div className="w-2 h-2 bg-primary rounded-full mt-2"></div>
              <div>
                <strong>Community Relevance:</strong> Consider how well the template fits sybau culture and humor
              </div>
            </li>
            <li className="flex items-start gap-3">
              <div className="w-2 h-2 bg-primary rounded-full mt-2"></div>
              <div>
                <strong>Versatility:</strong> Some templates work for multiple types of messages
              </div>
            </li>
          </ul>
        </section>

        {/* Section 3: Text Techniques */}
        <section id="text-techniques" className="mb-12">
          <div className="flex items-center gap-3 mb-6">
            <Type className="w-8 h-8 text-primary" />
            <h2 className="text-3xl font-bold mb-0">3. Text Placement and Typography</h2>
          </div>
          
          <p className="text-lg mb-6">
            Great memes aren't just about funny ideas—they're about presenting those ideas in a visually appealing 
            and readable way. Master these text techniques to make your memes stand out.
          </p>

          <h3 className="text-xl font-semibold mb-4">Typography Best Practices:</h3>
          <div className="space-y-6 mb-8">
            <div className="p-6 border border-border rounded-lg">
              <h4 className="font-semibold mb-3 flex items-center gap-2">
                <Palette className="w-5 h-5 text-primary" />
                Font Selection
              </h4>
              <p className="text-muted-foreground mb-4">
                Choose fonts that are bold, readable, and match your meme's personality. Impact and Arial Black 
                are classic choices for memes because they're highly legible.
              </p>
              <div className="bg-accent/20 p-4 rounded-lg">
                <p className="text-sm"><strong>Pro Tip:</strong> Stick to sans-serif fonts for better readability on small screens.</p>
              </div>
            </div>

            <div className="p-6 border border-border rounded-lg">
              <h4 className="font-semibold mb-3">Text Positioning</h4>
              <p className="text-muted-foreground mb-4">
                Strategic text placement can make or break your meme. Follow these guidelines:
              </p>
              <ul className="space-y-2 text-muted-foreground">
                <li>• <strong>Top text:</strong> Setup or context</li>
                <li>• <strong>Bottom text:</strong> Punchline or conclusion</li>
                <li>• <strong>Center text:</strong> Single powerful statement</li>
                <li>• <strong>Avoid covering faces:</strong> Unless it's intentional for effect</li>
              </ul>
            </div>

            <div className="p-6 border border-border rounded-lg">
              <h4 className="font-semibold mb-3">Readability Optimization</h4>
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <h5 className="font-medium mb-2 text-green-600">✅ Do:</h5>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• Use high contrast colors</li>
                    <li>• Add text outlines or shadows</li>
                    <li>• Keep text concise</li>
                    <li>• Test on mobile devices</li>
                  </ul>
                </div>
                <div>
                  <h5 className="font-medium mb-2 text-red-600">❌ Don't:</h5>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• Use too many fonts</li>
                    <li>• Make text too small</li>
                    <li>• Overcrowd with text</li>
                    <li>• Use low contrast combinations</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Section 4: Design Principles */}
        <section id="design-principles" className="mb-12">
          <div className="flex items-center gap-3 mb-6">
            <Palette className="w-8 h-8 text-primary" />
            <h2 className="text-3xl font-bold mb-0">4. Design Principles for Viral Memes</h2>
          </div>
          
          <p className="text-lg mb-6">
            Understanding basic design principles will elevate your memes from amateur to professional quality. 
            These principles apply whether you're using templates or creating custom content.
          </p>

          <div className="grid md:grid-cols-2 gap-8 mb-8">
            <div>
              <h3 className="text-xl font-semibold mb-4">Visual Hierarchy</h3>
              <p className="text-muted-foreground mb-4">
                Guide the viewer's eye through your meme in the right order. The most important element should 
                be the most prominent.
              </p>
              <ul className="space-y-2 text-muted-foreground">
                <li>• <strong>Primary:</strong> Main message or punchline</li>
                <li>• <strong>Secondary:</strong> Supporting text or context</li>
                <li>• <strong>Tertiary:</strong> Background elements</li>
              </ul>
            </div>
            
            <div>
              <h3 className="text-xl font-semibold mb-4">Color Psychology</h3>
              <p className="text-muted-foreground mb-4">
                Colors evoke emotions and can enhance your meme's message. Choose colors that support your content.
              </p>
              <ul className="space-y-2 text-muted-foreground">
                <li>• <strong>Red:</strong> Urgency, excitement, anger</li>
                <li>• <strong>Blue:</strong> Trust, calm, sadness</li>
                <li>• <strong>Yellow:</strong> Happiness, attention, caution</li>
                <li>• <strong>Green:</strong> Success, nature, money</li>
              </ul>
            </div>
          </div>
        </section>

        {/* Section 5: Sybau Culture */}
        <section id="sybau-culture" className="mb-12">
          <div className="flex items-center gap-3 mb-6">
            <Lightbulb className="w-8 h-8 text-primary" />
            <h2 className="text-3xl font-bold mb-0">5. Understanding Sybau Culture</h2>
          </div>

          <p className="text-lg mb-6">
            To create truly resonant sybau memes, you need to understand the community's unique culture,
            humor style, and shared experiences. This knowledge is what separates good memes from viral ones.
          </p>

          <div className="space-y-6 mb-8">
            <div className="p-6 bg-accent/10 rounded-lg">
              <h4 className="font-semibold mb-3">Content Values</h4>
              <p className="text-muted-foreground mb-4">
                Successful memes often reflect authenticity, creativity, and shared experiences. Content that embodies
                these values tends to perform better and create stronger connections with audiences.
              </p>
              <ul className="space-y-2 text-muted-foreground">
                <li>• <strong>Authenticity:</strong> Be genuine in your humor and messaging</li>
                <li>• <strong>Inclusivity:</strong> Create content that brings people together</li>
                <li>• <strong>Creativity:</strong> Put your own spin on popular formats</li>
                <li>• <strong>Respect:</strong> Avoid content that could harm or exclude others</li>
              </ul>
            </div>

            <div className="p-6 bg-accent/10 rounded-lg">
              <h4 className="font-semibold mb-3">Humor Styles That Work</h4>
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <h5 className="font-medium mb-2">Popular Formats:</h5>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• Relatable everyday situations</li>
                    <li>• Self-deprecating humor</li>
                    <li>• Pop culture references</li>
                    <li>• Unexpected twists</li>
                  </ul>
                </div>
                <div>
                  <h5 className="font-medium mb-2">Timing Considerations:</h5>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• Current events and trends</li>
                    <li>• Seasonal relevance</li>
                    <li>• Community milestones</li>
                    <li>• Viral moment opportunities</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Section 6: Advanced Techniques */}
        <section id="advanced-techniques" className="mb-12">
          <div className="flex items-center gap-3 mb-6">
            <Upload className="w-8 h-8 text-primary" />
            <h2 className="text-3xl font-bold mb-0">6. Advanced Creation Techniques</h2>
          </div>

          <p className="text-lg mb-6">
            Ready to take your meme game to the next level? These advanced techniques will help you create
            more sophisticated and engaging content that stands out from the crowd.
          </p>

          <div className="space-y-8 mb-8">
            <div className="p-6 border border-border rounded-lg">
              <h4 className="font-semibold mb-3 flex items-center gap-2">
                <Upload className="w-5 h-5 text-primary" />
                Custom Image Upload
              </h4>
              <p className="text-muted-foreground mb-4">
                While our templates are great, uploading your own images opens up unlimited creative possibilities.
                Here's how to make the most of custom uploads:
              </p>
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <h5 className="font-medium mb-2">Image Selection Tips:</h5>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• High resolution (at least 800px wide)</li>
                    <li>• Clear focal points</li>
                    <li>• Good contrast for text overlay</li>
                    <li>• Emotionally expressive subjects</li>
                  </ul>
                </div>
                <div>
                  <h5 className="font-medium mb-2">Technical Considerations:</h5>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• JPG or PNG formats work best</li>
                    <li>• Keep file sizes under 10MB</li>
                    <li>• Square or landscape orientations</li>
                    <li>• Avoid copyrighted material</li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="p-6 border border-border rounded-lg">
              <h4 className="font-semibold mb-3">Multi-Panel Storytelling</h4>
              <p className="text-muted-foreground mb-4">
                Create more complex narratives by combining multiple images or using templates with multiple panels.
                This technique is perfect for before/after scenarios, progression stories, or dialogue memes.
              </p>
              <div className="bg-accent/20 p-4 rounded-lg">
                <p className="text-sm"><strong>Pro Tip:</strong> Keep each panel's message clear and ensure the story flows logically from left to right, top to bottom.</p>
              </div>
            </div>
          </div>
        </section>

        {/* Section 7: Mobile Optimization */}
        <section id="mobile-optimization" className="mb-12">
          <div className="flex items-center gap-3 mb-6">
            <Smartphone className="w-8 h-8 text-primary" />
            <h2 className="text-3xl font-bold mb-0">7. Mobile-First Meme Design</h2>
          </div>

          <p className="text-lg mb-6">
            Most people will view your memes on mobile devices, so optimizing for small screens is crucial.
            Here's how to ensure your memes look great everywhere.
          </p>

          <div className="grid md:grid-cols-2 gap-8 mb-8">
            <div className="p-6 bg-accent/10 rounded-lg">
              <h4 className="font-semibold mb-3">Text Sizing</h4>
              <p className="text-muted-foreground mb-4">
                Text that looks perfect on desktop might be unreadable on mobile. Always test your memes on different screen sizes.
              </p>
              <ul className="space-y-2 text-muted-foreground">
                <li>• Minimum 24px font size for mobile</li>
                <li>• Use bold fonts for better visibility</li>
                <li>• Limit text to essential words only</li>
                <li>• Test readability at thumbnail size</li>
              </ul>
            </div>

            <div className="p-6 bg-accent/10 rounded-lg">
              <h4 className="font-semibold mb-3">Layout Considerations</h4>
              <p className="text-muted-foreground mb-4">
                Mobile screens are vertical, so design with portrait orientation in mind, even for landscape images.
              </p>
              <ul className="space-y-2 text-muted-foreground">
                <li>• Center important elements</li>
                <li>• Avoid placing text near edges</li>
                <li>• Use high contrast backgrounds</li>
                <li>• Keep compositions simple</li>
              </ul>
            </div>
          </div>
        </section>

        {/* Section 8: Sharing Strategies */}
        <section id="sharing-strategies" className="mb-12">
          <div className="flex items-center gap-3 mb-6">
            <Share2 className="w-8 h-8 text-primary" />
            <h2 className="text-3xl font-bold mb-0">8. Sharing and Distribution</h2>
          </div>

          <p className="text-lg mb-6">
            Creating a great meme is only half the battle. Strategic sharing and distribution are key to achieving viral success and reaching your target audience.
          </p>

          <div className="space-y-6 mb-8">
            <div className="p-6 border border-border rounded-lg">
              <h4 className="font-semibold mb-3">Platform Optimization</h4>
              <p className="text-muted-foreground mb-4">
                Different platforms have different optimal formats and posting strategies:
              </p>
              <div className="grid md:grid-cols-3 gap-4">
                <div className="text-center p-4 bg-accent/10 rounded-lg">
                  <h5 className="font-medium mb-2">Twitter/X</h5>
                  <p className="text-sm text-muted-foreground">Square format, trending hashtags, reply to popular tweets</p>
                </div>
                <div className="text-center p-4 bg-accent/10 rounded-lg">
                  <h5 className="font-medium mb-2">Instagram</h5>
                  <p className="text-sm text-muted-foreground">High quality, stories for behind-the-scenes, relevant hashtags</p>
                </div>
                <div className="text-center p-4 bg-accent/10 rounded-lg">
                  <h5 className="font-medium mb-2">Discord</h5>
                  <p className="text-sm text-muted-foreground">Community-specific humor, participate in conversations</p>
                </div>
              </div>
            </div>

            <div className="p-6 border border-border rounded-lg">
              <h4 className="font-semibold mb-3">Timing Your Posts</h4>
              <p className="text-muted-foreground mb-4">
                When you post can be just as important as what you post. Consider these timing strategies:
              </p>
              <ul className="space-y-2 text-muted-foreground">
                <li>• <strong>Peak Hours:</strong> 7-9 AM and 7-9 PM in your audience's timezone</li>
                <li>• <strong>Trending Moments:</strong> Jump on viral topics while they're hot</li>
                <li>• <strong>Trending Events:</strong> Align with current events and viral moments</li>
                <li>• <strong>Consistency:</strong> Regular posting builds audience expectations</li>
              </ul>
            </div>
          </div>
        </section>

        {/* Related Pages */}
        <RelatedPages currentPage="/guide" category="learning" maxItems={4} />

        {/* Call to Action */}
        <div className="text-center bg-gradient-to-r from-primary/20 to-primary/10 rounded-2xl p-12 mt-16">
          <h2 className="text-3xl font-bold mb-4">Ready to Create Your First Viral Meme?</h2>
          <p className="text-lg text-muted-foreground mb-8 max-w-2xl mx-auto">
            Put these techniques into practice with our easy-to-use meme generator. Start creating engaging
            sybau memes that your community will love to share.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/"
              className="inline-flex items-center gap-2 bg-primary text-primary-foreground px-8 py-4 rounded-lg font-semibold hover:bg-primary/90 transition-colors"
            >
              <Zap className="w-5 h-5" />
              Start Creating Now
            </a>
            <a
              href="/contact"
              className="inline-flex items-center gap-2 border border-border px-8 py-4 rounded-lg font-semibold hover:bg-accent transition-colors"
            >
              <Lightbulb className="w-5 h-5" />
              Get Expert Help
            </a>
          </div>
        </div>
      </div>
    </div>
  );
}
