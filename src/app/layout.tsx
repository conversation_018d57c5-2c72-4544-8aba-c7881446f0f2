import type { Metadata, Viewport } from "next";
import { Bricolage_Grotesque } from "next/font/google";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import PageTransition from "@/components/PageTransition";
import Breadcrumb from "@/components/Breadcrumb";
import "./globals.css";
import Providers from "./Provider";
import Script from "next/script";
import { Analytics } from "@vercel/analytics/next";

import { Toaster } from "sonner";
import { siteConfig } from "@/data/site-config";
import OGImage from "./og.png";

// Only load the essential font for initial page load
const bricolage_grotesque_init = Bricolage_Grotesque({
  subsets: ["latin"],
  display: "swap",
  preload: true,
});

export const metadata: Metadata = {
  title: {
    default: siteConfig.name,
    template: `%s | ${siteConfig.name}`,
  },
  description: siteConfig.description,
  metadataBase: new URL(siteConfig.url),
  authors: [{ name: 'Sybau Meme Generator Team' }],
  creator: 'Sybau Meme Generator',
  publisher: 'Sybau Meme Generator',
  applicationName: 'Sybau Meme Generator',
  referrer: 'origin-when-cross-origin',
  openGraph: {
    title: siteConfig.name,
    description: siteConfig.description,
    url: siteConfig.url,
    siteName: siteConfig.name,
    images: [
      {
        url: OGImage.src,
        width: OGImage.width,
        height: OGImage.height,
        alt: 'Sybau Meme Generator - Create Viral Memes Online',
      },
    ],
    locale: "en_US",
    type: "website",
  },
  twitter: {
    title: siteConfig.name,
    description: siteConfig.description,
    site: siteConfig.twitterHandle,
    creator: '@sybaumemes',
    card: "summary_large_image",
    images: [
      {
        url: OGImage.src,
        width: OGImage.width,
        height: OGImage.height,
        alt: 'Sybau Meme Generator - Create Viral Memes Online',
      },
    ],
  },
  alternates: {
    canonical: siteConfig.url,
  },
  robots: {
    index: true,
    follow: true,
    nocache: false,
    googleBot: {
      index: true,
      follow: true,
      noimageindex: false,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  category: 'Entertainment',
  classification: 'Meme Generator Tool',
};

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: '#ffffff' },
    { media: '(prefers-color-scheme: dark)', color: '#000000' },
  ],
  colorScheme: 'dark light',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <meta name="google-adsense-account" content="ca-pub-****************" />

        {/* Google AdSense */}
        <Script
          src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-****************"
          strategy="afterInteractive"
          crossOrigin="anonymous"
        />

        {/* Google Analytics */}
        <Script
          src="https://www.googletagmanager.com/gtag/js?id=G-0X4M7BF2WY"
          strategy="afterInteractive"
        />
        <Script id="google-analytics" strategy="afterInteractive">
          {`
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', 'G-0X4M7BF2WY');
          `}
        </Script>
      </head>
      <body
        className={`${bricolage_grotesque_init.className} antialiased min-h-screen bg-white dark:bg-black relative`}
        style={{
          fontFamily: `var(--font-bricolage-grotesque), Impact, "Arial Black", "Helvetica Neue", Arial, sans-serif`
        }}
      >
        <Providers>
          <PageTransition>
            <Navbar />
            <div className="container mx-auto">
              <Breadcrumb />
              <main className="px-4 py-8">{children}</main>
            </div>
            <Footer />
          </PageTransition>
          <Toaster />
        </Providers>
        <Analytics />
        
        <Script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-****************"
          strategy="beforeInteractive"
          crossOrigin="anonymous"
        />
      </body>
    </html>
  );
}
