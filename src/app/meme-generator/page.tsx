import { Metadata } from 'next';
import { siteConfig } from '@/data/site-config';
import MemeGeneratorPage from '@/components/MemeGeneratorPage';

export const metadata: Metadata = {
  title: 'Custom Sybau Meme Generator - Upload Your Own Images',
  description: 'Create custom sybau memes with your own images. Upload any photo and turn it into a viral meme template. Free and no signup required!',
  authors: [{ name: 'Sybau Meme Generator' }],
  creator: 'Sybau Meme Generator',
  publisher: 'Sybau Meme Generator',
  openGraph: {
    title: 'Custom Sybau Meme Generator - Upload Your Own Images',
    description: 'Create custom sybau memes with your own images. Upload any photo and turn it into a viral meme template. Free and no signup required!',
    url: `${siteConfig.url}/meme-generator`,
    siteName: siteConfig.name,
    images: [
      {
        url: `${siteConfig.url}/og.png`,
        width: 1200,
        height: 630,
        alt: 'Custom Sybau Meme Generator - Upload Your Own Images',
      },
    ],
    type: 'website',
    locale: 'en_US',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Custom Sybau Meme Generator - Upload Your Own Images',
    description: 'Create custom sybau memes with your own images. Upload any photo and turn it into a viral meme template. Free and no signup required!',
    images: [`${siteConfig.url}/og.png`],
    creator: '@sybaumemes',
    site: '@sybaumemes',
  },
  alternates: {
    canonical: `${siteConfig.url}/meme-generator`,
  },
  robots: {
    index: true,
    follow: true,
    nocache: false,
    googleBot: {
      index: true,
      follow: true,
      noimageindex: false,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  category: 'Entertainment',
};

export default function CustomMemeGeneratorPage() {
  return <MemeGeneratorPage />;
}
