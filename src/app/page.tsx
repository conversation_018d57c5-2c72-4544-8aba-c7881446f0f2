import HeroSection from "@/components/HeroSection";
import TemplateSearch from "@/components/TemplateSearch";
import FAQ from "@/components/FAQ";
import { siteConfig } from "@/data/site-config";
import Script from "next/script";
import StructuredData from "@/components/StructuredData";

export default function Home() {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    "name": "Sybau Meme Generator",
    "description": siteConfig.description,
    "url": siteConfig.url,
    "applicationCategory": "EntertainmentApplication",
    "operatingSystem": "Any",
    "offers": {
      "@type": "Offer",
      "price": "0",
      "priceCurrency": "USD"
    },
    "creator": {
      "@type": "Organization",
      "name": "Sybau Meme Generator"
    },
    "featureList": [
      "Free meme generator",
      "Hundreds of sybau meme templates",
      "Custom text editing",
      "Instant download",
      "No signup required",
      "Mobile-friendly interface"
    ],
    "screenshot": `${siteConfig.url}/og.png`,
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.8",
      "ratingCount": "1250",
      "bestRating": "5",
      "worstRating": "1"
    }
  };

  return (
    <>
      <StructuredData type="website" />
      <Script
        id="structured-data-webapp"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData),
        }}
      />
      <main>
        <HeroSection />
        <TemplateSearch />

        {/* Features Section */}
        <section className="py-16 bg-accent/20">
          <div className="max-w-6xl mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold mb-4">Why Choose Sybau Meme Generator?</h2>
              <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                The most comprehensive platform for creating viral sybau memes with professional tools and extensive resources.
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-8">
              <div className="text-center p-6">
                <div className="w-16 h-16 bg-primary/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">🎨</span>
                </div>
                <h3 className="text-xl font-semibold mb-3">Professional Tools</h3>
                <p className="text-muted-foreground">
                  Advanced editing features with intuitive interface designed for both beginners and experts.
                </p>
              </div>

              <div className="text-center p-6">
                <div className="w-16 h-16 bg-primary/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">🔥</span>
                </div>
                <h3 className="text-xl font-semibold mb-3">Trending Templates</h3>
                <p className="text-muted-foreground">
                  Constantly updated collection of popular meme templates based on current internet trends.
                </p>
              </div>

              <div className="text-center p-6">
                <div className="w-16 h-16 bg-primary/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">⚡</span>
                </div>
                <h3 className="text-xl font-semibold mb-3">Lightning Fast</h3>
                <p className="text-muted-foreground">
                  Create and download high-quality memes in seconds with our optimized platform.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Learning Resources */}
        <section className="py-16">
          <div className="max-w-6xl mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold mb-4">Master Meme Creation</h2>
              <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                From beginner tutorials to advanced techniques, we provide everything you need to create viral content.
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              <a href="/guide" className="group p-6 border border-border rounded-2xl hover:shadow-lg transition-all">
                <div className="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform">
                  <span className="text-2xl">📚</span>
                </div>
                <h3 className="text-xl font-semibold mb-3">Complete Guide</h3>
                <p className="text-muted-foreground">
                  Comprehensive tutorial covering everything from basics to advanced meme creation techniques.
                </p>
              </a>

              <a href="/tips" className="group p-6 border border-border rounded-2xl hover:shadow-lg transition-all">
                <div className="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform">
                  <span className="text-2xl">💡</span>
                </div>
                <h3 className="text-xl font-semibold mb-3">Pro Tips</h3>
                <p className="text-muted-foreground">
                  Expert strategies and insider secrets for creating memes that go viral in the sybau community.
                </p>
              </a>

              <a href="/resources" className="group p-6 border border-border rounded-2xl hover:shadow-lg transition-all">
                <div className="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform">
                  <span className="text-2xl">🛠️</span>
                </div>
                <h3 className="text-xl font-semibold mb-3">Resources</h3>
                <p className="text-muted-foreground">
                  Tools, templates, and inspiration to enhance your meme creation workflow and creativity.
                </p>
              </a>
            </div>
          </div>
        </section>

        <FAQ
          showCategories={true}
          title="Sybau Meme Generator FAQ"
          subtitle="Everything you need to know about creating viral sybau memes"
        />
      </main>
    </>
  );
}
