import { Metadata } from 'next';
import { siteConfig } from '@/data/site-config';
import { BookOpen, Video, Image, Palette, TrendingUp, Users, Download, ExternalLink } from 'lucide-react';

export const metadata: Metadata = {
  title: 'Meme Creation Resources - Tools, Guides & Inspiration | Sybau Meme Generator',
  description: 'Comprehensive collection of meme creation resources including tutorials, design tools, and guides for sybau meme creators.',
  openGraph: {
    title: 'Meme Creation Resources - Tools, Guides & Inspiration',
    description: 'Comprehensive collection of meme creation resources including tutorials, design tools, and guides for sybau meme creators.',
    url: `${siteConfig.url}/resources`,
    siteName: siteConfig.name,
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Meme Creation Resources - Tools, Guides & Inspiration',
    description: 'Comprehensive meme creation resources for the sybau community.',
  },
  alternates: {
    canonical: `${siteConfig.url}/resources`,
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default function ResourcesPage() {
  return (
    <div className="max-w-6xl mx-auto px-4 py-8">
      {/* Hero Section */}
      <div className="text-center mb-16">
        <div className="flex items-center justify-center gap-3 mb-6">
          <BookOpen className="w-12 h-12 text-primary" />
          <h1 className="text-5xl font-bold">
            Meme Creation <span className="sybau-text-gradient">Resources</span>
          </h1>
        </div>
        <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
          Everything you need to master the art of sybau meme creation. From beginner tutorials to advanced techniques, 
          find all the tools and inspiration you need in one place.
        </p>
      </div>

      {/* Resource Categories */}
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
        <div className="p-8 bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 rounded-2xl">
          <div className="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center mb-4">
            <BookOpen className="w-6 h-6 text-blue-600 dark:text-blue-400" />
          </div>
          <h3 className="text-xl font-semibold mb-3">Learning Guides</h3>
          <p className="text-muted-foreground mb-4">
            Comprehensive tutorials and step-by-step guides for all skill levels.
          </p>
          <div className="space-y-2">
            <a href="/guide" className="block text-blue-600 dark:text-blue-400 hover:underline text-sm">
              → Complete Meme Creation Guide
            </a>
            <a href="/tips" className="block text-blue-600 dark:text-blue-400 hover:underline text-sm">
              → Pro Tips for Viral Success
            </a>
            <a href="#beginner-guide" className="block text-blue-600 dark:text-blue-400 hover:underline text-sm">
              → Beginner's Quick Start
            </a>
          </div>
        </div>

        <div className="p-8 bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 rounded-2xl">
          <div className="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center mb-4">
            <Palette className="w-6 h-6 text-green-600 dark:text-green-400" />
          </div>
          <h3 className="text-xl font-semibold mb-3">Design Resources</h3>
          <p className="text-muted-foreground mb-4">
            Templates, fonts, and design elements to enhance your memes.
          </p>
          <div className="space-y-2">
            <a href="#templates" className="block text-green-600 dark:text-green-400 hover:underline text-sm">
              → Template Categories
            </a>
            <a href="#tools" className="block text-green-600 dark:text-green-400 hover:underline text-sm">
              → Recommended Tools
            </a>
            <a href="/" className="block text-green-600 dark:text-green-400 hover:underline text-sm">
              → Browse All Templates
            </a>
          </div>
        </div>

        <div className="p-8 bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 rounded-2xl">
          <div className="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center mb-4">
            <Users className="w-6 h-6 text-purple-600 dark:text-purple-400" />
          </div>
          <h3 className="text-xl font-semibold mb-3">Learning & Support</h3>
          <p className="text-muted-foreground mb-4">
            Get help and learn from comprehensive guides and tutorials.
          </p>
          <div className="space-y-2">
            <a href="/guide" className="block text-purple-600 dark:text-purple-400 hover:underline text-sm">
              → Complete Creation Guide
            </a>
            <a href="/tips" className="block text-purple-600 dark:text-purple-400 hover:underline text-sm">
              → Pro Tips & Strategies
            </a>
            <a href="/contact" className="block text-purple-600 dark:text-purple-400 hover:underline text-sm">
              → Contact Support
            </a>
          </div>
        </div>
      </div>

      {/* Featured Resources */}
      <div className="space-y-16">
        {/* Beginner's Guide */}
        <section id="beginner-guide">
          <div className="flex items-center gap-3 mb-8">
            <Video className="w-8 h-8 text-primary" />
            <h2 className="text-3xl font-bold">Quick Start for Beginners</h2>
          </div>
          
          <div className="grid md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-xl font-semibold mb-4">Your First Meme in 5 Minutes</h3>
              <p className="text-muted-foreground mb-6">
                New to meme creation? This quick guide will have you creating your first viral sybau meme in just 5 minutes.
              </p>
              
              <div className="space-y-4">
                <div className="flex items-start gap-4 p-4 bg-accent/10 rounded-lg">
                  <div className="w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center font-bold text-sm">1</div>
                  <div>
                    <h4 className="font-semibold mb-1">Choose a Popular Template</h4>
                    <p className="text-sm text-muted-foreground">Start with trending templates for better engagement</p>
                  </div>
                </div>
                
                <div className="flex items-start gap-4 p-4 bg-accent/10 rounded-lg">
                  <div className="w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center font-bold text-sm">2</div>
                  <div>
                    <h4 className="font-semibold mb-1">Keep Text Short & Punchy</h4>
                    <p className="text-sm text-muted-foreground">Maximum 10 words per text box for best impact</p>
                  </div>
                </div>
                
                <div className="flex items-start gap-4 p-4 bg-accent/10 rounded-lg">
                  <div className="w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center font-bold text-sm">3</div>
                  <div>
                    <h4 className="font-semibold mb-1">Test on Mobile</h4>
                    <p className="text-sm text-muted-foreground">Ensure your meme looks good on small screens</p>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="bg-accent/20 p-6 rounded-2xl">
              <h4 className="font-semibold mb-4">Essential Beginner Tips</h4>
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-primary rounded-full"></div>
                  <span className="text-sm">Use high contrast text colors</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-primary rounded-full"></div>
                  <span className="text-sm">Avoid covering important image elements</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-primary rounded-full"></div>
                  <span className="text-sm">Make text readable at thumbnail size</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-primary rounded-full"></div>
                  <span className="text-sm">Focus on relatable content</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-primary rounded-full"></div>
                  <span className="text-sm">Study what's trending in sybau community</span>
                </div>
              </div>
              
              <div className="mt-6 pt-6 border-t border-border">
                <a
                  href="/"
                  className="inline-flex items-center gap-2 bg-primary text-primary-foreground px-6 py-3 rounded-lg font-semibold hover:bg-primary/90 transition-colors w-full justify-center"
                >
                  <Image className="w-5 h-5" />
                  Start Creating Now
                </a>
              </div>
            </div>
          </div>
        </section>

        {/* Template Library */}
        <section id="templates">
          <div className="flex items-center gap-3 mb-8">
            <Image className="w-8 h-8 text-primary" />
            <h2 className="text-3xl font-bold">Template Categories</h2>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="p-6 border border-border rounded-lg hover:shadow-lg transition-shadow">
              <h4 className="font-semibold mb-2">🔥 Trending Now</h4>
              <p className="text-sm text-muted-foreground mb-3">
                Currently viral templates in the sybau community
              </p>
              <div className="text-xs text-muted-foreground">
                Updated daily • 25+ templates
              </div>
            </div>
            
            <div className="p-6 border border-border rounded-lg hover:shadow-lg transition-shadow">
              <h4 className="font-semibold mb-2">😂 Classic Memes</h4>
              <p className="text-sm text-muted-foreground mb-3">
                Timeless formats that never go out of style
              </p>
              <div className="text-xs text-muted-foreground">
                Evergreen • 50+ templates
              </div>
            </div>
            
            <div className="p-6 border border-border rounded-lg hover:shadow-lg transition-shadow">
              <h4 className="font-semibold mb-2">🎭 Reaction Memes</h4>
              <p className="text-sm text-muted-foreground mb-3">
                Perfect for expressing emotions and reactions
              </p>
              <div className="text-xs text-muted-foreground">
                High engagement • 30+ templates
              </div>
            </div>
            
            <div className="p-6 border border-border rounded-lg hover:shadow-lg transition-shadow">
              <h4 className="font-semibold mb-2">🌟 Sybau Exclusive</h4>
              <p className="text-sm text-muted-foreground mb-3">
                Templates created specifically for sybau culture
              </p>
              <div className="text-xs text-muted-foreground">
                Community favorite • 40+ templates
              </div>
            </div>
          </div>
        </section>

        {/* Tools & Resources */}
        <section id="tools">
          <div className="flex items-center gap-3 mb-8">
            <Palette className="w-8 h-8 text-primary" />
            <h2 className="text-3xl font-bold">Recommended Tools</h2>
          </div>
          
          <div className="grid md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-xl font-semibold mb-4">Free Design Tools</h3>
              <div className="space-y-4">
                <div className="flex items-center gap-4 p-4 border border-border rounded-lg">
                  <div className="w-10 h-10 bg-primary/20 rounded-lg flex items-center justify-center">
                    <ExternalLink className="w-5 h-5 text-primary" />
                  </div>
                  <div className="flex-1">
                    <h4 className="font-semibold">Canva</h4>
                    <p className="text-sm text-muted-foreground">Great for custom graphics and advanced layouts</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-4 p-4 border border-border rounded-lg">
                  <div className="w-10 h-10 bg-primary/20 rounded-lg flex items-center justify-center">
                    <ExternalLink className="w-5 h-5 text-primary" />
                  </div>
                  <div className="flex-1">
                    <h4 className="font-semibold">GIMP</h4>
                    <p className="text-sm text-muted-foreground">Free alternative to Photoshop for advanced editing</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-4 p-4 border border-border rounded-lg">
                  <div className="w-10 h-10 bg-primary/20 rounded-lg flex items-center justify-center">
                    <ExternalLink className="w-5 h-5 text-primary" />
                  </div>
                  <div className="flex-1">
                    <h4 className="font-semibold">Figma</h4>
                    <p className="text-sm text-muted-foreground">Professional design tool with collaboration features</p>
                  </div>
                </div>
              </div>
            </div>
            
            <div>
              <h3 className="text-xl font-semibold mb-4">Font Resources</h3>
              <div className="space-y-4">
                <div className="p-4 bg-accent/10 rounded-lg">
                  <h4 className="font-semibold mb-2">Best Meme Fonts</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Impact</span>
                      <span className="text-muted-foreground">Classic meme font</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Arial Black</span>
                      <span className="text-muted-foreground">High readability</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Helvetica Bold</span>
                      <span className="text-muted-foreground">Clean and modern</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Oswald</span>
                      <span className="text-muted-foreground">Condensed style</span>
                    </div>
                  </div>
                </div>
                
                <div className="p-4 bg-accent/10 rounded-lg">
                  <h4 className="font-semibold mb-2">Font Guidelines</h4>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• Use bold weights for better visibility</li>
                    <li>• Stick to sans-serif fonts</li>
                    <li>• Ensure high contrast with background</li>
                    <li>• Test readability at small sizes</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>

      {/* Call to Action */}
      <div className="text-center bg-gradient-to-r from-primary/20 to-primary/10 rounded-2xl p-12 mt-16">
        <h2 className="text-3xl font-bold mb-4">Start Your Meme Journey Today</h2>
        <p className="text-lg text-muted-foreground mb-8 max-w-2xl mx-auto">
          With these resources at your fingertips, you have everything you need to create amazing sybau memes. 
          Join thousands of creators who are already making viral content.
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <a
            href="/"
            className="inline-flex items-center gap-2 bg-primary text-primary-foreground px-8 py-4 rounded-lg font-semibold hover:bg-primary/90 transition-colors"
          >
            <Image className="w-5 h-5" />
            Create Your First Meme
          </a>
          <a
            href="/guide"
            className="inline-flex items-center gap-2 border border-border px-8 py-4 rounded-lg font-semibold hover:bg-accent transition-colors"
          >
            <BookOpen className="w-5 h-5" />
            Read Complete Guide
          </a>
        </div>
      </div>
    </div>
  );
}
