import { MetadataRoute } from 'next'
import { baseUrl } from '@/lib/constants'
import { templates } from '@/data/templates'
import { createSafeSlug, isValidSlug } from '@/lib/template-utils'

// 静态导出配置
export const dynamic = 'force-static';

export default function sitemap(): MetadataRoute.Sitemap {
  const now = new Date();

  // Generate template pages with safe URLs
  const templatePages = Object.keys(templates).map((templateKey) => {
    const safeSlug = createSafeSlug(templateKey);
    return {
      url: `${baseUrl}/sybau-meme-template/${safeSlug}`,
      lastModified: now,
      changeFrequency: 'monthly' as const,
      priority: 0.8,
    };
  }).filter(page => {
    const slug = page.url.split('/sybau-meme-template/')[1];
    return slug && isValidSlug(slug); // 使用工具函数验证slug
  });

  return [
    {
      url: baseUrl,
      lastModified: now,
      changeFrequency: 'weekly',
      priority: 1,
    },
    {
      url: `${baseUrl}/meme-generator`,
      lastModified: now,
      changeFrequency: 'monthly',
      priority: 0.9,
    },
    {
      url: `${baseUrl}/sybau-meme-template`,
      lastModified: now,
      changeFrequency: 'weekly',
      priority: 0.9,
    },
    {
      url: `${baseUrl}/about`,
      lastModified: now,
      changeFrequency: 'monthly',
      priority: 0.8,
    },
    {
      url: `${baseUrl}/contact`,
      lastModified: now,
      changeFrequency: 'monthly',
      priority: 0.7,
    },
    {
      url: `${baseUrl}/privacy-policy`,
      lastModified: now,
      changeFrequency: 'yearly',
      priority: 0.5,
    },
    {
      url: `${baseUrl}/terms-of-service`,
      lastModified: now,
      changeFrequency: 'yearly',
      priority: 0.5,
    },
    {
      url: `${baseUrl}/guide`,
      lastModified: now,
      changeFrequency: 'monthly',
      priority: 0.9,
    },
    {
      url: `${baseUrl}/tips`,
      lastModified: now,
      changeFrequency: 'weekly',
      priority: 0.8,
    },
    {
      url: `${baseUrl}/resources`,
      lastModified: now,
      changeFrequency: 'weekly',
      priority: 0.8,
    },
    ...templatePages,
  ]
}
