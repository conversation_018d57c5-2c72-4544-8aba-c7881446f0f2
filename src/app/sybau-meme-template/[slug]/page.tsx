import { Metadata, Viewport } from 'next';
import { notFound } from 'next/navigation';
import { templates } from '@/data/templates';
import TemplateDetailPage from '@/components/TemplateDetailPage';
import { siteConfig } from '@/data/site-config';
import { createSafeSlug, findTemplateKeyBySlug } from '@/lib/template-utils';

type Props = {
  params: Promise<{ slug: string }>;
};

// 生成静态参数
export async function generateStaticParams() {
  return Object.keys(templates).map((templateKey) => ({
    slug: createSafeSlug(templateKey),
  })).filter(param => param.slug); // 过滤掉空slug
}

// 根据slug查找模板
function getTemplateBySlug(slug: string) {
  const templateKey = findTemplateKeyBySlug(slug, templates);

  if (!templateKey) return null;

  return {
    key: templateKey,
    template: templates[templateKey as keyof typeof templates],
  };
}

// 生成页面元数据
export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { slug } = await params;
  const templateData = getTemplateBySlug(slug);

  if (!templateData) {
    return {
      title: 'Template Not Found - Sybau Meme Generator',
      description: 'The requested meme template could not be found. Browse our collection of viral sybau meme templates.',
      robots: {
        index: false,
        follow: false,
      },
    };
  }

  const templateName = templateData.key.replace(/-/g, ' ');
  const capitalizedTemplateName = templateName.split(' ').map(word =>
    word.charAt(0).toUpperCase() + word.slice(1)
  ).join(' ');

  const title = `${capitalizedTemplateName} Sybau Meme Generator - Create Viral ${capitalizedTemplateName} Memes`;
  const description = `Create hilarious ${templateName} sybau memes instantly with our free online meme generator. Customize text, add your personal touch, and create viral ${templateName} content. Download and share your ${templateName} memes instantly - no signup required!`;

  // 构建完整的图片URL
  const imageUrl = templateData.template.image.startsWith('http')
    ? templateData.template.image
    : `${siteConfig.url}${templateData.template.image}`;

  return {
    title,
    description,
    keywords: [
      `${templateName} sybau meme`,
      `${templateName} meme generator`,
      `${templateName} meme template`,
      `create ${templateName} meme`,
      `${templateName} meme maker`,
      `sybau ${templateName}`,
      'sybau meme generator',
      'viral meme creator',
      'free meme generator',
      'online meme creator',
      'meme templates',
      'sybau content',
    ],
    authors: [{ name: 'Sybau Meme Generator' }],
    creator: 'Sybau Meme Generator',
    publisher: 'Sybau Meme Generator',
    openGraph: {
      title,
      description,
      url: `${siteConfig.url}/sybau-meme-template/${slug}`,
      siteName: siteConfig.name,
      images: [
        {
          url: imageUrl,
          width: 1200,
          height: 630,
          alt: `${capitalizedTemplateName} sybau meme template - Create viral ${templateName} memes`,
        },
      ],
      type: 'website',
      locale: 'en_US',
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: [imageUrl],
      creator: '@sybaumemes',
      site: '@sybaumemes',
    },
    alternates: {
      canonical: `${siteConfig.url}/sybau-meme-template/${slug}`,
    },
    robots: {
      index: true,
      follow: true,
      nocache: false,
      googleBot: {
        index: true,
        follow: true,
        noimageindex: false,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    category: 'Entertainment',
  };
}

// 生成viewport配置
export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: '#ffffff' },
    { media: '(prefers-color-scheme: dark)', color: '#000000' },
  ],
  colorScheme: 'dark light',
};

export default async function TemplatePage({ params }: Props) {
  const { slug } = await params;
  const templateData = getTemplateBySlug(slug);
  
  if (!templateData) {
    notFound();
  }

  return (
    <TemplateDetailPage
      templateKey={templateData.key}
      template={templateData.template}
    />
  );
}
