import { Metadata } from 'next';
import { siteConfig } from '@/data/site-config';
import TemplateSearch from '@/components/TemplateSearch';
import FAQ from '@/components/FAQ';
import Script from 'next/script';
import StructuredData from '@/components/StructuredData';

export const metadata: Metadata = {
  title: 'Sybau Meme Templates - Browse All Templates | Sybau Meme Generator',
  description: 'Browse our complete collection of sybau meme templates. Find the perfect template for your viral meme creation. Free, instant, and easy to use.',
  keywords: [
    'sybau meme templates',
    'meme templates',
    'sybau templates',
    'viral meme templates',
    'free meme templates',
    'meme generator templates',
    'sybau meme collection'
  ],
  authors: [{ name: 'Sybau Meme Generator' }],
  creator: 'Sybau Meme Generator',
  publisher: 'Sybau Meme Generator',
  openGraph: {
    title: 'Sybau Meme Templates - Browse All Templates | Sybau Meme Generator',
    description: 'Browse our complete collection of sybau meme templates. Find the perfect template for your viral meme creation. Free, instant, and easy to use.',
    url: `${siteConfig.url}/sybau-meme-template`,
    siteName: siteConfig.name,
    images: [
      {
        url: `${siteConfig.url}/og.png`,
        width: 1200,
        height: 630,
        alt: 'Sybau Meme Templates Collection - Create Viral Sybau Memes',
      },
    ],
    type: 'website',
    locale: 'en_US',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Sybau Meme Templates - Browse All Templates | Sybau Meme Generator',
    description: 'Browse our complete collection of sybau meme templates. Find the perfect template for your viral meme creation. Free, instant, and easy to use.',
    images: [`${siteConfig.url}/og.png`],
    creator: '@sybaumemes',
    site: '@sybaumemes',
  },
  alternates: {
    canonical: `${siteConfig.url}/sybau-meme-template`,
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

export default function TemplatesPage() {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "CollectionPage",
    "name": "Sybau Meme Templates Collection",
    "description": "Browse our complete collection of sybau meme templates. Find the perfect template for your viral meme creation.",
    "url": `${siteConfig.url}/sybau-meme-template`,
    "mainEntity": {
      "@type": "ItemList",
      "name": "Sybau Meme Templates",
      "description": "Collection of viral sybau meme templates for creating hilarious content",
      "numberOfItems": "100+",
      "itemListElement": [
        {
          "@type": "CreativeWork",
          "name": "Sybau Meme Templates",
          "description": "Professional meme templates for creating viral sybau content"
        }
      ]
    },
    "breadcrumb": {
      "@type": "BreadcrumbList",
      "itemListElement": [
        {
          "@type": "ListItem",
          "position": 1,
          "name": "Home",
          "item": siteConfig.url
        },
        {
          "@type": "ListItem",
          "position": 2,
          "name": "Templates",
          "item": `${siteConfig.url}/sybau-meme-template`
        }
      ]
    }
  };

  return (
    <>
      <StructuredData type="website" />
      <Script
        id="templates-structured-data"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData),
        }}
      />
      <main>
        {/* Hero Section for Templates */}
        <section className="py-16 bg-gradient-to-br from-primary/10 via-accent/5 to-secondary/10">
          <div className="max-w-6xl mx-auto px-4 text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
              Sybau Meme Templates
            </h1>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-8">
              Discover our complete collection of viral sybau meme templates. From classic formats to trending designs, 
              find the perfect template to create your next viral masterpiece.
            </p>
            <div className="flex flex-wrap justify-center gap-4 text-sm text-muted-foreground">
              <span className="bg-accent/20 px-3 py-1 rounded-full">🔥 100+ Templates</span>
              <span className="bg-accent/20 px-3 py-1 rounded-full">⚡ Instant Download</span>
              <span className="bg-accent/20 px-3 py-1 rounded-full">🎨 Professional Quality</span>
              <span className="bg-accent/20 px-3 py-1 rounded-full">📱 Mobile Friendly</span>
            </div>
          </div>
        </section>

        {/* Template Search and Grid */}
        <TemplateSearch />

        {/* Additional Information */}
        <section className="py-16 bg-accent/20">
          <div className="max-w-6xl mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold mb-4">Why Our Templates Stand Out</h2>
              <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                Each template is carefully curated and optimized for maximum viral potential in the sybau community.
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-8">
              <div className="text-center p-6">
                <div className="w-16 h-16 bg-primary/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">🎯</span>
                </div>
                <h3 className="text-xl font-semibold mb-3">Trending Formats</h3>
                <p className="text-muted-foreground">
                  Stay ahead with templates based on the latest viral trends and popular meme formats.
                </p>
              </div>

              <div className="text-center p-6">
                <div className="w-16 h-16 bg-primary/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">✨</span>
                </div>
                <h3 className="text-xl font-semibold mb-3">High Quality</h3>
                <p className="text-muted-foreground">
                  Professional-grade templates optimized for clarity and impact across all platforms.
                </p>
              </div>

              <div className="text-center p-6">
                <div className="w-16 h-16 bg-primary/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl">🚀</span>
                </div>
                <h3 className="text-xl font-semibold mb-3">Easy to Use</h3>
                <p className="text-muted-foreground">
                  One-click selection with intuitive editing tools for quick meme creation.
                </p>
              </div>
            </div>
          </div>
        </section>

        <FAQ
          showCategories={true}
          title="Template FAQ"
          subtitle="Common questions about our sybau meme templates"
        />
      </main>
    </>
  );
}
