import { Metadata } from 'next';
import { siteConfig } from '@/data/site-config';

export const metadata: Metadata = {
  title: 'Terms of Service - Sybau Meme Generator',
  description: 'Terms of Service for Sybau Meme Generator. Read our terms and conditions for using our free online meme generator.',
  openGraph: {
    title: 'Terms of Service - Sybau Meme Generator',
    description: 'Terms of Service for Sybau Meme Generator. Read our terms and conditions for using our free online meme generator.',
    url: `${siteConfig.url}/terms-of-service`,
    siteName: siteConfig.name,
    type: 'website',
  },
  twitter: {
    card: 'summary',
    title: 'Terms of Service - Sybau Meme Generator',
    description: 'Terms of Service for Sybau Meme Generator. Read our terms and conditions for using our service.',
  },
  alternates: {
    canonical: `${siteConfig.url}/terms-of-service`,
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default function TermsOfServicePage() {
  return (
    <div className="max-w-4xl mx-auto px-4 py-8">
      <div className="prose prose-lg dark:prose-invert max-w-none">
        <h1 className="text-4xl font-bold mb-8 text-center">Terms of Service</h1>
        
        <div className="bg-accent/20 p-6 rounded-lg mb-8">
          <p className="text-sm text-muted-foreground mb-2">
            <strong>Last Updated:</strong> {new Date().toLocaleDateString()}
          </p>
          <p className="text-sm">
            These Terms of Service ("Terms") govern your use of the Sybau Meme Generator website and services. By using our service, you agree to these terms.
          </p>
        </div>

        <h2 className="text-2xl font-semibold mt-8 mb-4">1. Acceptance of Terms</h2>
        <p className="mb-4">
          By accessing or using Sybau Meme Generator ("Service"), you agree to be bound by these Terms of Service and our Privacy Policy. 
          If you do not agree to these terms, please do not use our service.
        </p>

        <h2 className="text-2xl font-semibold mt-8 mb-4">2. Description of Service</h2>
        <p className="mb-4">
          Sybau Meme Generator is a free online platform that allows users to create memes using pre-designed templates or by uploading their own images. 
          Our service includes:
        </p>
        <ul className="list-disc pl-6 space-y-2">
          <li>Access to a library of sybau meme templates</li>
          <li>Tools to add custom text to images</li>
          <li>Ability to upload and edit custom images</li>
          <li>Download functionality for created memes</li>
          <li>Mobile-responsive interface</li>
        </ul>

        <h2 className="text-2xl font-semibold mt-8 mb-4">3. User Responsibilities</h2>
        
        <h3 className="text-xl font-medium mt-6 mb-3">3.1 Acceptable Use</h3>
        <p className="mb-4">You agree to use our service only for lawful purposes and in accordance with these Terms. You must not:</p>
        <ul className="list-disc pl-6 space-y-2">
          <li>Upload or create content that is illegal, harmful, threatening, abusive, or offensive</li>
          <li>Infringe upon the intellectual property rights of others</li>
          <li>Use the service to harass, bully, or harm others</li>
          <li>Attempt to gain unauthorized access to our systems</li>
          <li>Use automated tools to scrape or download content</li>
          <li>Create content that violates any applicable laws or regulations</li>
        </ul>

        <h3 className="text-xl font-medium mt-6 mb-3">3.2 Content Responsibility</h3>
        <p className="mb-4">
          You are solely responsible for any content you upload, create, or share using our service. You represent and warrant that:
        </p>
        <ul className="list-disc pl-6 space-y-2">
          <li>You own or have the necessary rights to use any uploaded content</li>
          <li>Your content does not violate any third-party rights</li>
          <li>Your content complies with all applicable laws and regulations</li>
        </ul>

        <h2 className="text-2xl font-semibold mt-8 mb-4">4. Intellectual Property Rights and Disclaimers</h2>

        <h3 className="text-xl font-medium mt-6 mb-3">4.1 Our Platform</h3>
        <p className="mb-4">
          The Sybau Meme Generator website, including its design, functionality, and software code, is owned by us and protected by copyright and other intellectual property laws. This does not include the meme template images themselves.
        </p>

        <h3 className="text-xl font-medium mt-6 mb-3">4.2 Meme Templates and Content Disclaimer</h3>
        <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 p-4 rounded-lg mb-4">
          <p className="text-sm font-semibold text-yellow-800 dark:text-yellow-200 mb-2">⚠️ Important Copyright Notice</p>
          <p className="text-sm text-yellow-700 dark:text-yellow-300">
            The meme templates provided on our platform are based on popular internet meme formats and images that are widely circulated online.
            We do not claim ownership of these template images or the original source materials.
          </p>
        </div>
        <p className="mb-4">
          Our meme templates are provided for educational, parody, and fair use purposes. Many of these templates may be subject to copyright
          protection by their original creators or rights holders. By using our service, you acknowledge and agree that:
        </p>
        <ul className="list-disc pl-6 space-y-2 mb-4">
          <li>You are solely responsible for ensuring your use of any template complies with applicable copyright laws</li>
          <li>You must obtain proper permissions for commercial use of copyrighted materials</li>
          <li>We make no warranties regarding the copyright status of any template</li>
          <li>You will not hold us liable for any copyright infringement claims related to your use of templates</li>
        </ul>

        <h3 className="text-xl font-medium mt-6 mb-3">4.3 User-Generated Content</h3>
        <p className="mb-4">
          You retain ownership of any original content you create using our service. By using our platform to create and download memes,
          you represent that you have the right to use all elements included in your creation.
        </p>

        <h3 className="text-xl font-medium mt-6 mb-3">4.4 Copyright Compliance</h3>
        <p className="mb-4">
          Users are responsible for:
        </p>
        <ul className="list-disc pl-6 space-y-2 mb-4">
          <li>Ensuring their memes comply with fair use guidelines or obtaining proper licensing</li>
          <li>Respecting the intellectual property rights of original content creators</li>
          <li>Understanding that parody and commentary may be protected under fair use, but commercial use may require permission</li>
          <li>Removing or modifying content if requested by legitimate copyright holders</li>
        </ul>

        <h2 className="text-2xl font-semibold mt-8 mb-4">5. Privacy and Data Protection</h2>
        <p className="mb-4">
          Your privacy is important to us. Please review our Privacy Policy to understand how we collect, use, and protect your information. 
          By using our service, you consent to the collection and use of your information as described in our Privacy Policy.
        </p>

        <h2 className="text-2xl font-semibold mt-8 mb-4">6. Service Availability</h2>
        <p className="mb-4">
          We strive to maintain high availability of our service, but we do not guarantee uninterrupted access. We may temporarily suspend or restrict access for maintenance, updates, or other operational reasons.
        </p>

        <h2 className="text-2xl font-semibold mt-8 mb-4">7. Disclaimers and Limitations</h2>

        <h3 className="text-xl font-medium mt-6 mb-3">7.1 Service Disclaimer</h3>
        <p className="mb-4">
          Our service is provided "as is" without warranties of any kind. We do not guarantee that the service will meet your requirements or be error-free.
        </p>

        <h3 className="text-xl font-medium mt-6 mb-3">7.2 Copyright and Content Disclaimer</h3>
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 p-4 rounded-lg mb-4">
          <p className="text-sm font-semibold text-red-800 dark:text-red-200 mb-2">🚨 Important Legal Disclaimer</p>
          <p className="text-sm text-red-700 dark:text-red-300">
            We provide meme templates for creative and educational purposes. Users are solely responsible for ensuring
            their use of any content complies with applicable copyright laws and regulations.
          </p>
        </div>
        <p className="mb-4">
          We disclaim all responsibility for:
        </p>
        <ul className="list-disc pl-6 space-y-2 mb-4">
          <li>The copyright status of any template or content provided on our platform</li>
          <li>Any copyright infringement claims arising from user-created content</li>
          <li>The accuracy of any copyright information or fair use guidance</li>
          <li>Any legal consequences resulting from users' content creation or distribution</li>
        </ul>

        <h3 className="text-xl font-medium mt-6 mb-3">7.3 Limitation of Liability</h3>
        <p className="mb-4">
          To the maximum extent permitted by law, we shall not be liable for any indirect, incidental, special, or consequential damages arising from your use of our service, including but not limited to copyright infringement claims, legal fees, or damages related to content creation.
        </p>

        <h2 className="text-2xl font-semibold mt-8 mb-4">8. User Conduct and Community Guidelines</h2>
        <p className="mb-4">
          To maintain a positive environment for all users, we expect respectful and appropriate behavior. Content that promotes hate speech, violence, or discrimination is strictly prohibited.
        </p>

        <h2 className="text-2xl font-semibold mt-8 mb-4">9. Termination</h2>
        <p className="mb-4">
          We reserve the right to terminate or suspend access to our service at any time, with or without cause, and with or without notice, for conduct that we believe violates these Terms or is harmful to other users or our service.
        </p>

        <h2 className="text-2xl font-semibold mt-8 mb-4">10. Changes to Terms</h2>
        <p className="mb-4">
          We may modify these Terms at any time. We will notify users of significant changes by posting the updated Terms on our website. 
          Your continued use of the service after changes constitutes acceptance of the new Terms.
        </p>

        <h2 className="text-2xl font-semibold mt-8 mb-4">11. Governing Law</h2>
        <p className="mb-4">
          These Terms shall be governed by and construed in accordance with applicable laws, without regard to conflict of law principles.
        </p>

        <h2 className="text-2xl font-semibold mt-8 mb-4">12. Contact Information</h2>
        <p className="mb-4">
          If you have any questions about these Terms of Service, please contact us:
        </p>
        <div className="bg-accent/20 p-4 rounded-lg">
          <p><strong>Email:</strong> <EMAIL></p>
          <p><strong>Website:</strong> <a href={siteConfig.url} className="text-primary hover:underline">{siteConfig.url}</a></p>
        </div>

        <div className="mt-12 p-6 bg-primary/10 rounded-lg">
          <h3 className="text-lg font-semibold mb-2">Responsible Use Guidelines</h3>
          <p className="text-sm mb-3">
            Sybau Meme Generator is designed for creative expression and entertainment. We encourage responsible use of our platform:
          </p>
          <ul className="text-sm space-y-1">
            <li>• Always respect copyright and intellectual property rights</li>
            <li>• Consider fair use principles when creating content</li>
            <li>• Obtain proper permissions for commercial use</li>
            <li>• Create original, respectful content that brings joy to others</li>
            <li>• When in doubt about copyright, consult with legal professionals</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
