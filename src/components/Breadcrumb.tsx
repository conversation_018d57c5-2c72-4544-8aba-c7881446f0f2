'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { ChevronRight, Home } from 'lucide-react';

interface BreadcrumbItem {
  label: string;
  href: string;
}

export default function Breadcrumb() {
  const pathname = usePathname();
  
  // Don't show breadcrumb on home page
  if (pathname === '/') {
    return null;
  }

  const pathSegments = pathname.split('/').filter(Boolean);
  
  // Create breadcrumb items
  const breadcrumbItems: BreadcrumbItem[] = [
    { label: 'Home', href: '/' }
  ];

  // Build breadcrumb path
  let currentPath = '';
  pathSegments.forEach((segment, index) => {
    currentPath += `/${segment}`;
    
    // Convert segment to readable label
    let label = segment.replace(/-/g, ' ');
    label = label.charAt(0).toUpperCase() + label.slice(1);
    
    // Special cases for better labels
    switch (segment) {
      case 'sybau-meme-template':
        label = 'Templates';
        break;
      case 'meme-generator':
        label = 'Meme Generator';
        break;
      case 'privacy-policy':
        label = 'Privacy Policy';
        break;
      case 'terms-of-service':
        label = 'Terms of Service';
        break;
      default:
        // For template slugs, make them more readable
        if (pathSegments[index - 1] === 'sybau-meme-template') {
          label = `${label} Template`;
        }
    }
    
    breadcrumbItems.push({
      label,
      href: currentPath
    });
  });

  return (
    <nav className="flex items-center space-x-2 text-sm text-muted-foreground mb-6 px-4">
      {breadcrumbItems.map((item, index) => (
        <div key={item.href} className="flex items-center">
          {index > 0 && <ChevronRight className="w-4 h-4 mx-2" />}
          
          {index === 0 ? (
            <Link 
              href={item.href}
              className="flex items-center gap-1 hover:text-foreground transition-colors"
            >
              <Home className="w-4 h-4" />
              <span>{item.label}</span>
            </Link>
          ) : index === breadcrumbItems.length - 1 ? (
            <span className="text-foreground font-medium">{item.label}</span>
          ) : (
            <Link 
              href={item.href}
              className="hover:text-foreground transition-colors"
            >
              {item.label}
            </Link>
          )}
        </div>
      ))}
    </nav>
  );
}
