import Link from 'next/link';

export default function Footer() {
    return (
        <footer className="py-12 w-full bg-gradient-to-t from-accent/20 to-transparent border-t border-border/50">
            <div className="max-w-7xl mx-auto px-4">
                <div className="text-center space-y-6">
                    {/* Brand Section */}
                    <div className="space-y-2">
                        <h3 className="text-2xl font-bold">
                            <span className="sybau-text-gradient">Sybau</span> Meme Generator
                        </h3>
                        <p className="text-muted-foreground max-w-md mx-auto">
                            The ultimate destination for creating viral sybau memes. Express your creativity and make your mark!
                        </p>
                    </div>

                    {/* Features */}
                    <div className="flex flex-wrap justify-center gap-6 text-sm text-muted-foreground">
                        <span className="flex items-center gap-1">
                            🔥 <strong>Exclusive Templates</strong>
                        </span>
                        <span className="flex items-center gap-1">
                            ⚡ <strong>Instant Creation</strong>
                        </span>
                        <span className="flex items-center gap-1">
                            📱 <strong>Mobile Ready</strong>
                        </span>
                        <span className="flex items-center gap-1">
                            🎨 <strong>Custom Upload</strong>
                        </span>
                    </div>

                    {/* Navigation Links */}
                    <div className="flex flex-wrap justify-center gap-6 text-sm">
                        <Link href="/guide" className="text-muted-foreground hover:text-foreground transition-colors">
                            Guide
                        </Link>
                        <Link href="/tips" className="text-muted-foreground hover:text-foreground transition-colors">
                            Tips
                        </Link>
                        <Link href="/resources" className="text-muted-foreground hover:text-foreground transition-colors">
                            Resources
                        </Link>
                        <Link href="/about" className="text-muted-foreground hover:text-foreground transition-colors">
                            About Us
                        </Link>
                        <Link href="/contact" className="text-muted-foreground hover:text-foreground transition-colors">
                            Contact
                        </Link>
                        <Link href="/privacy-policy" className="text-muted-foreground hover:text-foreground transition-colors">
                            Privacy Policy
                        </Link>
                        <Link href="/terms-of-service" className="text-muted-foreground hover:text-foreground transition-colors">
                            Terms of Service
                        </Link>
                    </div>

                    {/* Copyright */}
                    <div className="text-center text-xs text-muted-foreground">
                        <p>&copy; {new Date().getFullYear()} Sybau Meme Generator. All rights reserved.</p>
                        <p className="mt-1">Made with ❤️ for creative expression</p>
                    </div>

                </div>
            </div>
        </footer>
    )
}