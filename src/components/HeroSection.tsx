"use client"

import useSelected from "@/hooks/useSelected";

export default function HeroSection() {
    const { selected } = useSelected()
    return (
        <section className={`justify-center pt-12 pb-8 relative ${selected ? 'hidden' : 'flex'}`}>
            <div className="flex gap-8 flex-col lg:flex-row items-center max-w-7xl mx-auto px-4">
                {/* 左侧内容 */}
                <div className="flex gap-4 flex-col flex-1 text-center lg:text-left">
                    <h1 className="text-4xl md:text-5xl lg:text-6xl max-w-4xl tracking-tighter font-regular">
                        <span className="font-bold animate-gradient bg-gradient-to-r from-[#6a7bd1] via-[#8a9cf1] to-[#6a7bd1] bg-clip-text text-transparent bg-[length:200%_auto] animate-gradient-flow">
                            Sybau Meme Generator
                        </span>
                        <br/>
                        <span className="text-muted-foreground text-2xl md:text-3xl lg:text-4xl font-medium">
                            Create Viral Memes Instantly
                        </span>
                    </h1>
                    <p className="text-lg md:text-xl leading-relaxed tracking-tight text-muted-foreground max-w-3xl mx-auto lg:mx-0">
                        The ultimate sybau meme generator! Create hilarious memes with our exclusive sybau templates or upload your own content. Join the viral meme revolution - no signup required!
                    </p>
                </div>

                {/* 右侧视频 */}
                <div className="flex-shrink-0 w-80 h-80 lg:w-96 lg:h-96">
                    <div className="relative w-full h-full rounded-2xl overflow-hidden shadow-2xl bg-gradient-to-br from-[#6a7bd1]/20 to-[#8a9cf1]/20 p-2">
                        <video
                            className="w-full h-full object-cover rounded-xl"
                            autoPlay
                            muted
                            loop
                            playsInline
                            poster="/logo.png"
                        >
                            <source src="/sybau.mp4" type="video/mp4" />
                            Your browser does not support the video tag.
                        </video>
                        <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-xl pointer-events-none"></div>
                    </div>
                </div>
            </div>
        </section>
    )
}