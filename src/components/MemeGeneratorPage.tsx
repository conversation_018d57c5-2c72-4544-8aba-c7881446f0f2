"use client";

import React, { useEffect, Suspense } from 'react';
import { motion } from 'framer-motion';
import { Upload, Spark<PERSON>, Zap, Heart } from 'lucide-react';
import CustomTemplateUpload from './CustomTemplateUpload';
import DynamicMemeEditor from './DynamicMemeEditor';
import FAQ from './FAQ';
import RelatedTemplates from './RelatedTemplates';
import useSelected from '@/hooks/useSelected';
import { siteConfig } from '@/data/site-config';
import Script from 'next/script';

import { Template } from '@/types/template';

// 内部组件处理meme生成器内容
function MemeGeneratorContent() {
  const { handleCustomTemplateSelect, customTemplate, isCustomTemplate, resetSelection } = useSelected();

  // 处理浏览器返回按钮
  useEffect(() => {
    const handlePopState = () => {
      // 如果有自定义模板状态，清除它并返回主页
      if (isCustomTemplate) {
        resetSelection();
        // 使用replace避免循环
        window.location.replace('/');
        return;
      }
    };

    window.addEventListener('popstate', handlePopState);

    return () => {
      window.removeEventListener('popstate', handlePopState);
    };
  }, [isCustomTemplate, resetSelection]);

  // 处理自定义模板上传（在当前页面内）
  const handleCustomTemplateUpload = (template: Template) => {
    handleCustomTemplateSelect(template);
    // 简单地设置状态，不需要复杂的历史记录操作
  };

  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": "Custom Sybau Meme Generator",
    "description": "Create custom sybau memes with your own images. Upload any photo and turn it into a viral meme template.",
    "url": `${siteConfig.url}/meme-generator`,
    "mainEntity": {
      "@type": "SoftwareApplication",
      "name": "Custom Sybau Meme Generator",
      "applicationCategory": "EntertainmentApplication",
      "operatingSystem": "Any",
      "offers": {
        "@type": "Offer",
        "price": "0",
        "priceCurrency": "USD"
      },
      "featureList": [
        "Upload custom images",
        "Create personalized memes",
        "Add custom text",
        "Download high-quality memes",
        "Free to use",
        "No registration required"
      ]
    },
    "breadcrumb": {
      "@type": "BreadcrumbList",
      "itemListElement": [
        {
          "@type": "ListItem",
          "position": 1,
          "name": "Home",
          "item": siteConfig.url
        },
        {
          "@type": "ListItem",
          "position": 2,
          "name": "Custom Meme Generator",
          "item": `${siteConfig.url}/meme-generator`
        }
      ]
    }
  };

  return (
    <div className="max-w-7xl mx-auto py-8">
      <Script
        id="meme-generator-structured-data"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData),
        }}
      />
      
      {/* Hero Section */}
      <div className="text-center mb-12">
        <motion.h1 
          className="text-4xl md:text-6xl font-bold mb-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          Custom <span className="sybau-text-gradient">Sybau</span> Meme Generator
        </motion.h1>
        
        <motion.p 
          className="text-xl text-muted-foreground max-w-3xl mx-auto mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
        >
          Transform any image into a viral sybau meme! Upload your own photos and create personalized 
          meme templates with our powerful custom meme generator.
        </motion.p>

        <motion.div 
          className="flex flex-wrap justify-center gap-6 mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <div className="flex items-center gap-2 text-sm">
            <Sparkles className="w-5 h-5 text-yellow-500" />
            <span><strong>Unlimited Uploads</strong></span>
          </div>
          <div className="flex items-center gap-2 text-sm">
            <Zap className="w-5 h-5 text-blue-500" />
            <span><strong>Instant Processing</strong></span>
          </div>
          <div className="flex items-center gap-2 text-sm">
            <Heart className="w-5 h-5 text-red-500" />
            <span><strong>100% Free</strong></span>
          </div>
        </motion.div>
      </div>

      {/* Main Content */}
      {isCustomTemplate && customTemplate ? (
        <motion.div 
          className="bg-gradient-to-br from-accent/10 to-primary/5 rounded-2xl p-8 sybau-border mb-12"
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.6, delay: 0.3 }}
        >
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold mb-4">
              Edit Your Custom Meme
            </h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Your image has been uploaded successfully! Add text, customize styling, and create your viral sybau meme.
            </p>
          </div>

          <div className="w-full max-sm:w-full mx-auto flex flex-col flex-wrap items-center">
            <div className="w-full max-w-5xl">
              <DynamicMemeEditor
                template={customTemplate}
                showBackButton={false}
              />
            </div>
          </div>
        </motion.div>
      ) : (
        <motion.div 
          className="bg-gradient-to-br from-accent/10 to-primary/5 rounded-2xl p-8 sybau-border mb-12"
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.6, delay: 0.3 }}
        >
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold mb-4">
              Upload Your Image
            </h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Choose any image from your device or paste from clipboard. We&apos;ll automatically 
              create text boxes and optimize it for meme creation.
            </p>
          </div>

          <div className="flex justify-center">
            <CustomTemplateUpload
              onTemplateCreate={handleCustomTemplateUpload}
              buttonText="Upload Image"
              buttonIcon={<Upload className="h-5 w-5" />}
              buttonClassName="px-8 py-4 bg-primary text-primary-foreground hover:bg-primary/90 transition-colors flex items-center gap-3 text-lg font-semibold rounded-xl sybau-glow"
              title="Upload Custom Image"
              description="Select an image file or paste from clipboard to create your custom sybau meme template."
            />
          </div>
        </motion.div>
      )}

      {/* Related Templates Section */}
      <RelatedTemplates
        currentTemplateKey="sybau"
        maxItems={8}
      />

      {/* FAQ Section */}
      <FAQ
        title="Custom Sybau Meme Generator FAQ"
        subtitle="Everything you need to know about creating custom sybau memes"
        maxItems={8}
        templateSpecific="custom template"
      />

    </div>
  );
}

// 主导出组件，包装Suspense边界
export default function MemeGeneratorPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading meme generator...</p>
        </div>
      </div>
    }>
      <MemeGeneratorContent />
    </Suspense>
  );
}
