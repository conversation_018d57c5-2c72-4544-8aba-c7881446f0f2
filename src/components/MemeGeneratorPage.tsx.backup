"use client";

import React from 'react';
import { motion } from 'framer-motion';
import { Upload, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Heart } from 'lucide-react';
import CustomTemplateUpload from './CustomTemplateUpload';
import DynamicMemeEditor from './DynamicMemeEditor';
import useSelected from '@/hooks/useSelected';
import { siteConfig } from '@/data/site-config';
import Script from 'next/script';

export default function MemeGeneratorPage() {
  const { handleCustomTemplateSelect, customTemplate, isCustomTemplate } = useSelected();

  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": "Custom Sybau Meme Generator",
    "description": "Create custom sybau memes with your own images. Upload any photo and turn it into a viral meme template.",
    "url": `${siteConfig.url}/meme-generator`,
    "mainEntity": {
      "@type": "SoftwareApplication",
      "name": "Custom Sybau Meme Generator",
      "applicationCategory": "EntertainmentApplication",
      "operatingSystem": "Any",
      "offers": {
        "@type": "Offer",
        "price": "0",
        "priceCurrency": "USD"
      },
      "featureList": [
        "Upload custom images",
        "Create personalized memes",
        "Add custom text",
        "Download high-quality memes",
        "Free to use",
        "No registration required"
      ]
    },
    "breadcrumb": {
      "@type": "BreadcrumbList",
      "itemListElement": [
        {
          "@type": "ListItem",
          "position": 1,
          "name": "Home",
          "item": siteConfig.url
        },
        {
          "@type": "ListItem",
          "position": 2,
          "name": "Custom Meme Generator",
          "item": `${siteConfig.url}/meme-generator`
        }
      ]
    }
  };

  return (
    <>
      <Script
        id="meme-generator-structured-data"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData),
        }}
      />
      
      <div className="max-w-7xl mx-auto py-8">
        {/* Hero Section - Always visible */}
        <div className="text-center mb-12">
          <motion.h1
            className="text-4xl md:text-6xl font-bold mb-6"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            Custom <span className="sybau-text-gradient">Sybau</span> Meme Generator
          </motion.h1>

          <motion.p
            className="text-xl text-muted-foreground max-w-3xl mx-auto mb-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
          >
            Transform any image into a viral sybau meme! Upload your own photos and create personalized
            meme templates with our powerful custom meme generator.
          </motion.p>

          <motion.div
            className="flex flex-wrap justify-center gap-6 mb-12"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <div className="flex items-center gap-2 text-sm">
              <Sparkles className="w-5 h-5 text-yellow-500" />
              <span><strong>Unlimited Uploads</strong></span>
            </div>
            <div className="flex items-center gap-2 text-sm">
              <Zap className="w-5 h-5 text-blue-500" />
              <span><strong>Instant Processing</strong></span>
            </div>
            <div className="flex items-center gap-2 text-sm">
              <Heart className="w-5 h-5 text-red-500" />
              <span><strong>100% Free</strong></span>
            </div>
          </motion.div>
        </div>

        {/* Upload Section or Editor Section */}
        {isCustomTemplate && customTemplate ? (
          <motion.div
            className="bg-gradient-to-br from-accent/10 to-primary/5 rounded-2xl p-8 sybau-border mb-12"
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold mb-4">
                Edit Your Custom Meme
              </h2>
              <p className="text-muted-foreground max-w-2xl mx-auto">
                Your image has been uploaded successfully! Add text, customize styling, and create your viral sybau meme.
              </p>
            </div>

            <div className="w-full max-sm:w-full mx-auto flex flex-col flex-wrap items-center">
              <div className="w-full max-w-5xl">
                <DynamicMemeEditor
                  template={customTemplate}
                  showBackButton={true}
                />
              </div>
            </div>
          </motion.div>
        )}

        {/* Upload Section - Only show when no custom template */}
        {!isCustomTemplate && (
          <>
            <motion.div
              className="bg-gradient-to-br from-accent/10 to-primary/5 rounded-2xl p-8 sybau-border mb-12"
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              <div className="text-center mb-8">
                <h2 className="text-3xl font-bold mb-4">
                  Upload Your Image
                </h2>
                <p className="text-muted-foreground max-w-2xl mx-auto">
                  Choose any image from your device or paste from clipboard. We&apos;ll automatically
                  create text boxes and optimize it for meme creation.
                </p>
              </div>

              <div className="flex justify-center">
                <CustomTemplateUpload
                  onTemplateCreate={handleCustomTemplateSelect}
                  buttonText="Upload Image"
                  buttonIcon={<Upload className="h-5 w-5" />}
                  buttonClassName="px-8 py-4 bg-primary text-primary-foreground hover:bg-primary/90 transition-colors flex items-center gap-3 text-lg font-semibold rounded-xl sybau-glow"
                  title="Upload Custom Image"
                  description="Select an image file or paste from clipboard to create your custom sybau meme template."
                />
              </div>
            </motion.div>

            {/* Features Section */}
            <motion.div 
              className="grid md:grid-cols-3 gap-8 mb-12"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              <div className="text-center p-6 rounded-xl bg-accent/20">
                <div className="w-16 h-16 bg-primary/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Upload className="w-8 h-8 text-primary" />
                </div>
                <h3 className="text-xl font-semibold mb-2">Easy Upload</h3>
                <p className="text-muted-foreground">
                  Drag & drop, browse files, or paste images directly from your clipboard
                </p>
              </div>

              <div className="text-center p-6 rounded-xl bg-accent/20">
                <div className="w-16 h-16 bg-primary/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Sparkles className="w-8 h-8 text-primary" />
                </div>
                <h3 className="text-xl font-semibold mb-2">Auto Optimization</h3>
                <p className="text-muted-foreground">
                  Automatic text box placement and image optimization for perfect memes
                </p>
              </div>

              <div className="text-center p-6 rounded-xl bg-accent/20">
                <div className="w-16 h-16 bg-primary/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Zap className="w-8 h-8 text-primary" />
                </div>
                <h3 className="text-xl font-semibold mb-2">Instant Creation</h3>
                <p className="text-muted-foreground">
                  Start editing immediately with our powerful meme editor tools
                </p>
              </div>
            </motion.div>

            {/* How It Works Section */}
            <motion.div 
              className="text-center mb-12"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
            >
              <h2 className="text-3xl font-bold mb-8">How to Create Custom Sybau Memes</h2>
              
              <div className="grid md:grid-cols-4 gap-6">
                <div className="text-center">
                  <div className="w-12 h-12 bg-primary text-primary-foreground rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                    1
                  </div>
                  <h3 className="font-semibold mb-2">Upload Image</h3>
                  <p className="text-sm text-muted-foreground">Choose any image from your device</p>
                </div>
                
                <div className="text-center">
                  <div className="w-12 h-12 bg-primary text-primary-foreground rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                    2
                  </div>
                  <h3 className="font-semibold mb-2">Add Text</h3>
                  <p className="text-sm text-muted-foreground">Customize text with our editor</p>
                </div>
                
                <div className="text-center">
                  <div className="w-12 h-12 bg-primary text-primary-foreground rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                    3
                  </div>
                  <h3 className="font-semibold mb-2">Style It</h3>
                  <p className="text-sm text-muted-foreground">Adjust fonts, colors, and positioning</p>
                </div>
                
                <div className="text-center">
                  <div className="w-12 h-12 bg-primary text-primary-foreground rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                    4
                  </div>
                  <h3 className="font-semibold mb-2">Download</h3>
                  <p className="text-sm text-muted-foreground">Save and share your viral meme</p>
                </div>
              </div>
            </motion.div>
          </>
        )}
      </div>
    </>
  );
}
