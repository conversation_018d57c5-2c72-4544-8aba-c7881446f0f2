"use client"

import { MoonIcon, SunIcon } from "lucide-react"
import { useTheme } from "next-themes"
import Link from "next/link"
import { useEffect, useState } from "react"
import { useRouter, usePathname } from "next/navigation"
import useSelected from "@/hooks/useSelected"

export default function Navbar() {
    const [mounted, setMounted] = useState(false)
    const { theme, setTheme } = useTheme()
    const router = useRouter()
    const pathname = usePathname()
    const { resetSelection, isCustomTemplate } = useSelected()

    // 处理Logo点击，智能导航到主页
    const handleLogoClick = (e: React.MouseEvent) => {
        e.preventDefault()

        // 如果在meme-generator页面且有自定义模板，清除状态
        if (pathname === '/meme-generator' && isCustomTemplate) {
            resetSelection()
        }

        // 总是返回主页
        router.replace('/')
    }

    useEffect(() => {
        setMounted(true)
    }, [])

    if (!mounted) {
        return null
    }

    return (
        <nav className="w-full bg-white dark:bg-black">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="flex items-center justify-between h-16">
                    <div className="flex-shrink-0">
                        <Link href="/" onClick={handleLogoClick} className="cursor-pointer">
                            <h1 className="text-xl font-bold text-gray-900 dark:text-white flex items-center gap-1">
                                <span className="bg-gradient-to-r from-[#6a7bd1] via-[#8a9cf1] to-[#6a7bd1] bg-clip-text text-transparent font-extrabold">
                                    Sybau
                                </span>
                                <span className="text-xl">
                                    💔
                                </span>
                            </h1>
                        </Link>
                    </div>
                    <div className="flex items-center gap-4">
                        {/* Navigation Links */}
                        <div className="hidden md:flex items-center gap-6">
                            <Link
                                href="/guide"
                                className="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
                            >
                                Guide
                            </Link>
                            <Link
                                href="/resources"
                                className="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
                            >
                                Resources
                            </Link>
                            <Link
                                href="/about"
                                className="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
                            >
                                About
                            </Link>
                            <Link
                                href="/contact"
                                className="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors"
                            >
                                Contact
                            </Link>
                        </div>

                        <button
                            onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
                            className="p-2 rounded-md bg-black/80 text-white dark:bg-white/20 border dark:border-gray-200/20 transition-colors"
                        >
                            {theme === "dark" ? <SunIcon className="h-4 w-4" /> : <MoonIcon className="h-4 w-4" />}
                        </button>
                    </div>
                </div>
            </div>
        </nav>
    )
} 