import Link from 'next/link';
import { ArrowRight, BookOpen, Lightbulb, <PERSON>lette, Users, HelpCircle, Mail } from 'lucide-react';

interface RelatedPage {
  title: string;
  description: string;
  href: string;
  icon: React.ReactNode;
  category: 'learning' | 'tools' | 'community' | 'support';
}

interface RelatedPagesProps {
  currentPage?: string;
  maxItems?: number;
  category?: 'learning' | 'tools' | 'community' | 'support' | 'all';
}

const allPages: RelatedPage[] = [
  {
    title: 'Complete Meme Guide',
    description: 'Master the art of creating viral sybau memes with our comprehensive tutorial.',
    href: '/guide',
    icon: <BookOpen className="w-5 h-5" />,
    category: 'learning'
  },
  {
    title: 'Pro Tips & Strategies',
    description: 'Expert tips for creating memes that go viral in the sybau community.',
    href: '/tips',
    icon: <Lightbulb className="w-5 h-5" />,
    category: 'learning'
  },
  {
    title: 'Design Resources',
    description: 'Tools, templates, and inspiration for better meme creation.',
    href: '/resources',
    icon: <Palette className="w-5 h-5" />,
    category: 'tools'
  },
  {
    title: 'Meme Generator',
    description: 'Create custom memes by uploading your own images.',
    href: '/meme-generator',
    icon: <Palette className="w-5 h-5" />,
    category: 'tools'
  },
  {
    title: 'About Us',
    description: 'Learn about our mission, values, and commitment to the meme creation community.',
    href: '/about',
    icon: <Users className="w-5 h-5" />,
    category: 'community'
  },
  {
    title: 'FAQ & Help',
    description: 'Find answers to common questions about meme creation.',
    href: '/#faq',
    icon: <HelpCircle className="w-5 h-5" />,
    category: 'support'
  },
  {
    title: 'Contact Us',
    description: 'Get in touch with our team for support or feedback.',
    href: '/contact',
    icon: <Mail className="w-5 h-5" />,
    category: 'support'
  }
];

export default function RelatedPages({ 
  currentPage, 
  maxItems = 6, 
  category = 'all' 
}: RelatedPagesProps) {
  // Filter out current page and apply category filter
  let filteredPages = allPages.filter(page => page.href !== currentPage);
  
  if (category !== 'all') {
    filteredPages = filteredPages.filter(page => page.category === category);
  }
  
  // Limit number of items
  const displayPages = filteredPages.slice(0, maxItems);
  
  if (displayPages.length === 0) {
    return null;
  }

  const getCategoryTitle = (cat: string) => {
    switch (cat) {
      case 'learning': return 'Learning Resources';
      case 'tools': return 'Creation Tools';
      case 'community': return 'Community';
      case 'support': return 'Support';
      default: return 'Related Pages';
    }
  };

  return (
    <section className="py-12 bg-accent/20 rounded-2xl">
      <div className="max-w-6xl mx-auto px-6">
        <div className="text-center mb-8">
          <h2 className="text-2xl font-bold mb-2">
            {category !== 'all' ? getCategoryTitle(category) : 'Explore More'}
          </h2>
          <p className="text-muted-foreground">
            Discover more resources to enhance your sybau meme creation journey
          </p>
        </div>
        
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {displayPages.map((page) => (
            <Link
              key={page.href}
              href={page.href}
              className="group p-6 bg-background border border-border rounded-lg hover:shadow-lg transition-all duration-200 hover:border-primary/50"
            >
              <div className="flex items-start gap-4">
                <div className="w-10 h-10 bg-primary/20 rounded-lg flex items-center justify-center text-primary group-hover:scale-110 transition-transform">
                  {page.icon}
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold mb-2 group-hover:text-primary transition-colors">
                    {page.title}
                  </h3>
                  <p className="text-sm text-muted-foreground mb-3">
                    {page.description}
                  </p>
                  <div className="flex items-center text-primary text-sm font-medium">
                    <span>Learn more</span>
                    <ArrowRight className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" />
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>
        
        {category !== 'all' && filteredPages.length > maxItems && (
          <div className="text-center mt-8">
            <Link
              href="/resources"
              className="inline-flex items-center gap-2 bg-primary text-primary-foreground px-6 py-3 rounded-lg font-semibold hover:bg-primary/90 transition-colors"
            >
              View All Resources
              <ArrowRight className="w-4 h-4" />
            </Link>
          </div>
        )}
      </div>
    </section>
  );
}
