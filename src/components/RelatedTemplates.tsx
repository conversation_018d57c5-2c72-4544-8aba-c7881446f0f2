"use client";

import React, { useMemo } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { templates } from '@/data/templates';
import { ExternalLink } from 'lucide-react';
import { createSafeSlug } from '@/lib/template-utils';
import { Template } from '@/types/template';

interface RelatedTemplatesProps {
  currentTemplateKey: string;
  maxItems?: number;
}



export default function RelatedTemplates({ currentTemplateKey, maxItems = 8 }: RelatedTemplatesProps) {
  // 获取推荐模板（排除当前模板）- 使用稳定的排序避免水合错误
  const getRelatedTemplates = useMemo(() => {
    const allTemplates = Object.entries(templates);
    const filteredTemplates = allTemplates.filter(([key]) => key !== currentTemplateKey);

    // 使用基于模板key的稳定排序，避免随机性导致的水合错误
    const sorted = filteredTemplates.sort(([keyA], [keyB]) => {
      // 基于字符串哈希的伪随机排序，但在服务器和客户端保持一致
      const hashA = keyA.split('').reduce((a, b) => { a = ((a << 5) - a) + b.charCodeAt(0); return a & a; }, 0);
      const hashB = keyB.split('').reduce((a, b) => { a = ((a << 5) - a) + b.charCodeAt(0); return a & a; }, 0);
      return hashA - hashB;
    });

    return sorted.slice(0, maxItems);
  }, [currentTemplateKey, maxItems]);

  const relatedTemplates = getRelatedTemplates;

  if (relatedTemplates.length === 0) {
    return null;
  }

  return (
    <section className="py-12 bg-accent/20 rounded-2xl">
      <div className="max-w-6xl mx-auto px-4">
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold mb-4">Other Popular Templates</h2>
          <p className="text-muted-foreground">
            Explore more popular meme templates to create even more hilarious content
          </p>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-4 gap-6">
          {relatedTemplates.map(([templateKey, template]: [string, Template], index: number) => {
            const templateName = templateKey.replace(/-/g, ' ');
            const slug = createSafeSlug(templateKey);
            
            return (
              <motion.div
                key={templateKey}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1, duration: 0.3 }}
                className="group"
              >
                <Link href={`/sybau-meme-template/${slug}`} className="block">
                  <div className="relative aspect-square overflow-hidden rounded-xl bg-gray-100 dark:bg-gray-800">
                    <Image
                      src={template.image}
                      alt={`${templateName} meme template`}
                      fill
                      className="object-cover transition-transform duration-300 group-hover:scale-105"
                      sizes="(max-width: 768px) 50vw, (max-width: 1024px) 25vw, 20vw"
                    />
                    
                    {/* 悬停覆盖层 */}
                    <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center">
                      <div className="bg-white text-black px-3 py-1.5 rounded-md text-sm font-medium flex items-center gap-1">
                        <ExternalLink className="w-3 h-3" />
                        View Template
                      </div>
                    </div>
                  </div>
                  
                  <div className="mt-3 text-center">
                    <h3 className="font-semibold text-sm capitalize line-clamp-2">
                      {templateName}
                    </h3>
                  </div>
                </Link>
              </motion.div>
            );
          })}
        </div>

        <div className="text-center mt-8">
          <Link
            href="/"
            className="inline-flex items-center gap-2 bg-primary text-primary-foreground px-6 py-3 rounded-lg font-semibold hover:bg-primary/90 transition-colors"
          >
            Browse All Templates
          </Link>
        </div>
      </div>
    </section>
  );
}
