'use client';

import React from 'react';
import { motion } from 'framer-motion';

interface AFTScoreData {
  totalScore: number;
  status: 'Passed' | 'Failed';
  scores: {
    deadlift: number;
    pushUp: number;
    sprintDragCarry: number;
    plank: number;
    twoMileRun: number;
  };
  gender: string;
  ageGroup: string;
}

interface SocialMediaShareProps {
  /** AFT score data to share */
  scoreData?: AFTScoreData;
  /** The meme image as a data URL or blob URL */
  imageUrl?: string;
  /** Text to share along with the image */
  shareText?: string;
  /** URL to share (defaults to current page) */
  shareUrl?: string;
  /** Custom CSS classes */
  className?: string;
  /** Icon size for social buttons */
  iconSize?: number;
  /** Share type: 'meme' or 'aft' */
  shareType?: 'meme' | 'aft';
}

export default function SocialMediaShare({
  scoreData,
  imageUrl, // Currently not used but kept for future image sharing functionality
  shareText = "Check out this awesome meme I created! 🔥",
  shareUrl,
  className = "",
  iconSize = 32,
  shareType = 'meme'
}: SocialMediaShareProps) {
  // Generate sharing text based on share type
  const generateShareText = () => {
    if (shareType === 'aft' && scoreData) {
      const { totalScore, status, scores } = scoreData;
      return `🎯 Just completed my Army Fitness Test (AFT)!

📊 Overall Score: ${totalScore}/600 - ${status}!
• MDL: ${scores.deadlift} pts
• HRP: ${scores.pushUp} pts
• SDC: ${scores.sprintDragCarry} pts
• PLK: ${scores.plank} pts
• 2MR: ${scores.twoMileRun} pts

Calculate your AFT score at`;
    } else {
      return `${shareText} 🎨 Created with Sybau Meme Generator

🚀 Join the meme revolution at`;
    }
  };

  // Get current page URL dynamically
  const getCurrentUrl = () => {
    if (typeof window !== 'undefined') {
      try {
        // Check if we're in an iframe
        if (window.parent && window.parent !== window) {
          // Try to get parent URL (may fail due to cross-origin restrictions)
          try {
            return window.parent.location.href;
          } catch {
            // If we can't access parent URL due to cross-origin restrictions,
            // try to get it from document.referrer or use default
            if (document.referrer && document.referrer !== window.location.href) {
              return document.referrer;
            }
            // Fallback based on share type
            if (shareType === 'aft') {
              return shareUrl || 'https://aftcalculator.online/aft-calculator';
            }
            return shareUrl || 'https://sybaumeme.com';
          }
        } else {
          // Not in iframe, use current URL
          return shareUrl || window.location.href;
        }
      } catch {
        // Fallback in case of any errors
        if (shareType === 'aft') {
          return shareUrl || 'https://aftcalculator.online/aft-calculator';
        }
        return shareUrl || 'https://sybaumeme.com';
      }
    }
    if (shareType === 'aft') {
      return shareUrl || 'https://aftcalculator.online/aft-calculator';
    }
    return shareUrl || 'https://sybaumeme.com';
  };

  // Share to Twitter/X
  const shareToTwitter = () => {
    const text = generateShareText();
    const url = getCurrentUrl();
    const hashtags = shareType === 'aft' ? 'AFT,ArmyFitness,MilitaryFitness,AFTCalculator' : 'meme,SybauMeme,MemeGenerator,ViralMemes';

    const twitterUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(url)}&hashtags=${hashtags}`;
    window.open(twitterUrl, '_blank');
  };

  // Share to Facebook
  const shareToFacebook = () => {
    const url = getCurrentUrl();
    const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`;
    window.open(facebookUrl, '_blank');
  };

  // Share to WhatsApp
  const shareToWhatsApp = () => {
    const text = generateShareText();
    const url = getCurrentUrl();
    const message = `${text} ${url}`;

    const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
  };

  // Share to LINE
  const shareToLine = () => {
    const text = generateShareText();
    const url = getCurrentUrl();

    const lineUrl = `https://social-plugins.line.me/lineit/share?url=${encodeURIComponent(url)}&text=${encodeURIComponent(text)}`;
    window.open(lineUrl, '_blank');
  };

  // Share to Reddit
  const shareToReddit = () => {
    const text = generateShareText();
    const url = getCurrentUrl();
    const title = shareType === 'aft' && scoreData
      ? `My Army Fitness Test (AFT) Results - ${scoreData.totalScore}/600 ${scoreData.status}!`
      : `Check out this awesome Sybau meme I created! 🔥`;

    const redditUrl = `https://www.reddit.com/submit?url=${encodeURIComponent(url)}&title=${encodeURIComponent(title)}&text=${encodeURIComponent(text)}`;
    window.open(redditUrl, '_blank');
  };

  // Share to Telegram
  const shareToTelegram = () => {
    const text = generateShareText();
    const url = getCurrentUrl();

    const telegramUrl = `https://t.me/share/url?url=${encodeURIComponent(url)}&text=${encodeURIComponent(text)}`;
    window.open(telegramUrl, '_blank');
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, scale: 0.8 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        type: "spring" as const,
        stiffness: 300,
        damping: 20
      }
    }
  };

  return (
    <motion.div
      className={`flex items-center justify-center gap-2 ${className}`}
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Facebook */}
      <motion.button
        onClick={shareToFacebook}
        className="transition-transform hover:scale-110"
        title="Share on Facebook"
        variants={itemVariants}
      >
        <svg viewBox="0 0 64 64" width={iconSize} height={iconSize}>
          <circle cx="32" cy="32" r="32" fill="#0965FE" />
          <path d="M34.1,47V33.3h4.6l0.7-5.3h-5.3v-3.4c0-1.5,0.4-2.6,2.6-2.6l2.8,0v-4.8c-0.5-0.1-2.2-0.2-4.1-0.2 c-4.1,0-6.9,2.5-6.9,7V28H24v5.3h4.6V47H34.1z" fill="white" />
        </svg>
      </motion.button>

      {/* Twitter/X */}
      <motion.button
        onClick={shareToTwitter}
        className="transition-transform hover:scale-110"
        title="Share on X (Twitter)"
        variants={itemVariants}
      >
        <svg viewBox="0 0 64 64" width={iconSize} height={iconSize}>
          <circle cx="32" cy="32" r="32" fill="#000000" />
          <path d="M 41.116 18.375 h 4.962 l -10.8405 12.39 l 12.753 16.86 H 38.005 l -7.821 -10.2255 L 21.235 47.625 H 16.27 l 11.595 -13.2525 L 15.631 18.375 H 25.87 l 7.0695 9.3465 z m -1.7415 26.28 h 2.7495 L 24.376 21.189 H 21.4255 z" fill="white" />
        </svg>
      </motion.button>

      {/* Reddit */}
      <motion.button
        onClick={shareToReddit}
        className="transition-transform hover:scale-110"
        title="Share on Reddit"
        variants={itemVariants}
      >
        <svg viewBox="0 0 64 64" width={iconSize} height={iconSize}>
          <circle cx="32" cy="32" r="32" fill="#FF5700" />
          <path d="M 53.34375 32 C 53.277344 30.160156 52.136719 28.53125 50.429688 27.839844 C 48.722656 27.148438 46.769531 27.523438 45.441406 28.800781 C 41.800781 26.324219 37.519531 24.957031 33.121094 24.863281 L 35.199219 14.878906 L 42.046875 16.320312 C 42.214844 17.882812 43.496094 19.09375 45.066406 19.171875 C 46.636719 19.253906 48.03125 18.183594 48.359375 16.644531 C 48.6875 15.105469 47.847656 13.558594 46.382812 12.992188 C 44.914062 12.425781 43.253906 13.007812 42.464844 14.367188 L 34.625 12.800781 C 34.363281 12.742188 34.09375 12.792969 33.871094 12.9375 C 33.648438 13.082031 33.492188 13.308594 33.441406 13.566406 L 31.070312 24.671875 C 26.617188 24.738281 22.277344 26.105469 18.59375 28.609375 C 17.242188 27.339844 15.273438 26.988281 13.570312 27.707031 C 11.863281 28.429688 10.746094 30.089844 10.71875 31.941406 C 10.691406 33.789062 11.757812 35.484375 13.441406 36.257812 C 13.402344 36.726562 13.402344 37.195312 13.441406 37.664062 C 13.441406 44.832031 21.792969 50.65625 32.097656 50.65625 C 42.398438 50.65625 50.753906 44.832031 50.753906 37.664062 C 50.789062 37.195312 50.789062 36.726562 50.753906 36.257812 C 52.363281 35.453125 53.371094 33.800781 53.34375 32 Z M 21.34375 35.199219 C 21.34375 33.433594 22.777344 32 24.542969 32 C 26.3125 32 27.742188 33.433594 27.742188 35.199219 C 27.742188 36.96875 26.3125 38.398438 24.542969 38.398438 C 22.777344 38.398438 21.34375 36.96875 21.34375 35.199219 Z M 39.9375 44 C 37.664062 45.710938 34.871094 46.582031 32.03125 46.464844 C 29.191406 46.582031 26.398438 45.710938 24.128906 44 C 23.847656 43.65625 23.871094 43.15625 24.183594 42.839844 C 24.5 42.527344 25 42.503906 25.34375 42.785156 C 27.269531 44.195312 29.617188 44.90625 32 44.800781 C 34.386719 44.929688 36.746094 44.242188 38.6875 42.847656 C 39.042969 42.503906 39.605469 42.511719 39.953125 42.863281 C 40.296875 43.21875 40.289062 43.785156 39.9375 44.128906 Z M 39.359375 38.527344 C 37.59375 38.527344 36.160156 37.09375 36.160156 35.328125 C 36.160156 33.5625 37.59375 32.128906 39.359375 32.128906 C 41.128906 32.128906 42.558594 33.5625 42.558594 35.328125 C 42.59375 36.203125 42.269531 37.054688 41.65625 37.6875 C 41.046875 38.316406 40.203125 38.664062 39.328125 38.65625 Z M 39.359375 38.527344" fill="white" />
        </svg>
      </motion.button>

      {/* WhatsApp */}
      <motion.button
        onClick={shareToWhatsApp}
        className="transition-transform hover:scale-110"
        title="Share on WhatsApp"
        variants={itemVariants}
      >
        <svg viewBox="0 0 64 64" width={iconSize} height={iconSize}>
          <circle cx="32" cy="32" r="32" fill="#25D366" />
          <path d="m42.32286,33.93287c-0.5178,-0.2589 -3.04726,-1.49644 -3.52105,-1.66732c-0.4712,-0.17346 -0.81554,-0.2589 -1.15987,0.2589c-0.34175,0.51004 -1.33075,1.66474 -1.63108,2.00648c-0.30032,0.33658 -0.60064,0.36247 -1.11327,0.12945c-0.5178,-0.2589 -2.17994,-0.80259 -4.14759,-2.56312c-1.53269,-1.37217 -2.56312,-3.05503 -2.86603,-3.57283c-0.30033,-0.5178 -0.03366,-0.80259 0.22524,-1.06149c0.23301,-0.23301 0.5178,-0.59547 0.7767,-0.90616c0.25372,-0.31068 0.33657,-0.5178 0.51262,-0.85437c0.17088,-0.36246 0.08544,-0.64725 -0.04402,-0.90615c-0.12945,-0.2589 -1.15987,-2.79613 -1.58964,-3.80584c-0.41424,-1.00971 -0.84142,-0.88027 -1.15987,-0.88027c-0.29773,-0.02588 -0.64208,-0.02588 -0.98382,-0.02588c-0.34693,0 -0.90616,0.12945 -1.37736,0.62136c-0.4712,0.5178 -1.80194,1.76053 -1.80194,4.27186c0,2.51134 1.84596,4.945 2.10227,5.30747c0.2589,0.33657 3.63497,5.51458 8.80262,7.74113c1.23237,0.5178 2.1903,0.82848 2.94111,1.08738c1.23237,0.38836 2.35599,0.33657 3.24402,0.20712c0.99159,-0.15534 3.04985,-1.24272 3.47963,-2.45956c0.44013,-1.21683 0.44013,-2.22654 0.31068,-2.45955c-0.12945,-0.23301 -0.46601,-0.36247 -0.98382,-0.59548m-9.40068,12.84407l-0.02589,0c-3.05503,0 -6.08417,-0.82849 -8.72495,-2.38189l-0.62136,-0.37023l-6.47252,1.68286l1.73463,-6.29129l-0.41424,-0.64725c-1.70875,-2.71846 -2.6149,-5.85116 -2.6149,-9.07706c0,-9.39809 7.68934,-17.06155 17.15993,-17.06155c4.58253,0 8.88029,1.78642 12.11655,5.02268c3.23625,3.21036 5.02267,7.50812 5.02267,12.06476c-0.0078,9.3981 -7.69712,17.06155 -17.14699,17.06155m14.58906,-31.58846c-3.93529,-3.80584 -9.1133,-5.95471 -14.62789,-5.95471c-11.36055,0 -20.60848,9.2065 -20.61625,20.52564c0,3.61684 0.94757,7.14565 2.75211,10.26282l-2.92557,10.63564l10.93337,-2.85309c3.0136,1.63108 6.4052,2.4958 9.85634,2.49839l0.01037,0c11.36574,0 20.61884,-9.2091 20.62403,-20.53082c0,-5.48093 -2.14111,-10.64081 -6.03239,-14.51915" fill="white" />
        </svg>
      </motion.button>

      {/* LINE */}
      <motion.button
        onClick={shareToLine}
        className="transition-transform hover:scale-110"
        title="Share on LINE"
        variants={itemVariants}
      >
        <svg viewBox="0 0 64 64" width={iconSize} height={iconSize}>
          <circle cx="32" cy="32" r="32" fill="#00b800" />
          <path d="M52.62 30.138c0 3.693-1.432 7.019-4.42 10.296h.001c-4.326 4.979-14 11.044-16.201 11.972-2.2.927-1.876-.591-1.786-1.112l.294-1.765c.069-.527.142-1.343-.066-1.865-.232-.574-1.146-.872-1.817-1.016-9.909-1.31-17.245-8.238-17.245-16.51 0-9.226 9.251-16.733 20.62-16.733 11.37 0 20.62 7.507 20.62 16.733zM27.81 25.68h-1.446a.402.402 0 0 0-.402.401v8.985c0 .221.18.4.402.4h1.446a.401.401 0 0 0 .402-.4v-8.985a.402.402 0 0 0-.402-.401zm9.956 0H36.32a.402.402 0 0 0-.402.401v5.338L31.8 25.858a.39.39 0 0 0-.031-.04l-.002-.003-.024-.025-.008-.007a.313.313 0 0 0-.032-.026.255.255 0 0 1-.021-.014l-.012-.007-.021-.012-.013-.006-.023-.01-.013-.005-.024-.008-.014-.003-.023-.005-.017-.002-.021-.003-.021-.002h-1.46a.402.402 0 0 0-.402.401v8.985c0 .221.18.4.402.4h1.446a.401.401 0 0 0 .402-.4v-5.337l4.123 5.568c.028.04.063.072.101.099l.004.003a.236.236 0 0 0 .025.015l.012.006.019.01a.154.154 0 0 1 .019.008l.012.004.028.01.005.001a.442.442 0 0 0 .104.013h1.446a.4.4 0 0 0 .401-.4v-8.985a.402.402 0 0 0-.401-.401zm-13.442 7.537h-3.93v-7.136a.401.401 0 0 0-.401-.401h-1.447a.4.4 0 0 0-.401.401v8.984a.392.392 0 0 0 .123.29c.072.068.17.111.278.111h5.778a.4.4 0 0 0 .401-.401v-1.447a.401.401 0 0 0-.401-.401zm21.429-5.287c.222 0 .401-.18.401-.402v-1.446a.401.401 0 0 0-.401-.402h-5.778a.398.398 0 0 0-.279.113l-.005.004-.006.008a.397.397 0 0 0-.111.276v8.984c0 .108.043.206.112.278l.005.006a.401.401 0 0 0 .284.117h5.778a.4.4 0 0 0 .401-.401v-1.447a.401.401 0 0 0-.401-.401h-3.93v-1.519h3.93c.222 0 .401-.18.401-.402V29.85a.401.401 0 0 0-.401-.402h-3.93V27.93h3.93z" fill="white" />
        </svg>
      </motion.button>

      {/* Telegram */}
      <motion.button
        onClick={shareToTelegram}
        className="transition-transform hover:scale-110"
        title="Share on Telegram"
        variants={itemVariants}
      >
        <svg viewBox="0 0 64 64" width={iconSize} height={iconSize}>
          <circle cx="32" cy="32" r="32" fill="#25A3E3" />
          <path d="m45.90873,15.44335c-0.6901,-0.0281 -1.37668,0.14048 -1.96142,0.41265c-0.84989,0.32661 -8.63939,3.33986 -16.5237,6.39174c-3.9685,1.53296 -7.93349,3.06593 -10.98537,4.24067c-3.05012,1.1765 -5.34694,2.05098 -5.4681,2.09312c-0.80775,0.28096 -1.89996,0.63566 -2.82712,1.72788c-0.23354,0.27218 -0.46884,0.62161 -0.58825,1.10275c-0.11941,0.48114 -0.06673,1.09222 0.16682,1.5716c0.46533,0.96052 1.25376,1.35737 2.18443,1.71383c3.09051,0.99037 6.28638,1.93508 8.93263,2.8236c0.97632,3.44171 1.91401,6.89571 2.84116,10.34268c0.30554,0.69185 0.97105,0.94823 1.65764,0.95525l-0.00351,0.03512c0,0 0.53908,0.05268 1.06412,-0.07375c0.52679,-0.12292 1.18879,-0.42846 1.79109,-0.99212c0.662,-0.62161 2.45836,-2.38812 3.47683,-3.38552l7.6736,5.66477l0.06146,0.03512c0,0 0.84989,0.59703 2.09312,0.68132c0.62161,0.04214 1.4399,-0.07726 2.14229,-0.59176c0.70766,-0.51626 1.1765,-1.34683 1.396,-2.29506c0.65673,-2.86224 5.00979,-23.57745 5.75257,-27.00686l-0.02107,0.08077c0.51977,-1.93157 0.32837,-3.70159 -0.87096,-4.74991c-0.60054,-0.52152 -1.2924,-0.7498 -1.98425,-0.77965l0,0.00176zm-0.2072,3.29069c0.04741,0.0439 0.0439,0.0439 0.00351,0.04741c-0.01229,-0.00351 0.14048,0.2072 -0.15804,1.32576l-0.01229,0.04214l-0.00878,0.03863c-0.75858,3.50668 -5.15554,24.40802 -5.74203,26.96472c-0.08077,0.34417 -0.11414,0.31959 -0.09482,0.29852c-0.1756,-0.02634 -0.50045,-0.16506 -0.52679,-0.1756l-13.13468,-9.70175c4.4988,-4.33199 9.09945,-8.25307 13.744,-12.43229c0.8218,-0.41265 0.68483,-1.68573 -0.29852,-1.70681c-1.04305,0.24584 -1.92279,0.99564 -2.8798,1.47502c-5.49971,3.2626 -11.11882,6.13186 -16.55882,9.49279c-2.792,-0.97105 -5.57873,-1.77704 -8.15298,-2.57601c2.2336,-0.89555 4.00889,-1.55579 5.75608,-2.23009c3.05188,-1.1765 7.01687,-2.7042 10.98537,-4.24067c7.94051,-3.06944 15.92667,-6.16346 16.62028,-6.43037l0.05619,-0.02283l0.05268,-0.02283c0.19316,-0.0878 0.30378,-0.09658 0.35471,-0.10009c0,0 -0.01756,-0.05795 -0.00351,-0.04566l-0.00176,0zm-20.91715,22.0638l2.16687,1.60145c-0.93418,0.91311 -1.81743,1.77353 -2.45485,2.38812l0.28798,-3.98957" fill="white" />
        </svg>
      </motion.button>
    </motion.div>
  );
}
