'use client';

import React from 'react';
import { motion } from 'framer-motion';

interface SocialMediaShareProps {
  /** The meme image as a data URL or blob URL */
  imageUrl?: string;
  /** Text to share along with the image */
  shareText?: string;
  /** URL to share (defaults to current page) */
  shareUrl?: string;
  /** Custom CSS classes */
  className?: string;
  /** Icon size for social buttons */
  iconSize?: number;
}

export default function SocialMediaShare({
  imageUrl,
  shareText = "Check out this awesome meme I created! 🔥",
  shareUrl,
  className = "",
  iconSize = 32
}: SocialMediaShareProps) {
  // Generate sharing text
  const generateShareText = () => {
    return `${shareText} 🎨 Created with Sybau Meme Generator

🚀 Join the meme revolution at`;
  };

  // Get current page URL dynamically
  const getCurrentUrl = () => {
    if (typeof window !== 'undefined') {
      try {
        // Check if we're in an iframe
        if (window.parent && window.parent !== window) {
          // Try to get parent URL (may fail due to cross-origin restrictions)
          try {
            return window.parent.location.href;
          } catch {
            // If we can't access parent URL due to cross-origin restrictions,
            // try to get it from document.referrer or use default
            if (document.referrer && document.referrer !== window.location.href) {
              return document.referrer;
            }
            // Fallback to our main site URL
            return shareUrl || 'https://sybaumeme.com';
          }
        } else {
          // Not in iframe, use current URL
          return shareUrl || window.location.href;
        }
      } catch {
        // Fallback in case of any errors
        return shareUrl || 'https://sybaumeme.com';
      }
    }
    return shareUrl || 'https://sybaumeme.com';
  };

  // Share to Twitter/X
  const shareToTwitter = () => {
    const text = generateShareText();
    const url = getCurrentUrl();
    const hashtags = 'meme,SybauMeme,MemeGenerator,ViralMemes';

    const twitterUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(url)}&hashtags=${hashtags}`;
    window.open(twitterUrl, '_blank');
  };

  // Share to Facebook
  const shareToFacebook = () => {
    const url = getCurrentUrl();
    const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`;
    window.open(facebookUrl, '_blank');
  };

  // Share to WhatsApp
  const shareToWhatsApp = () => {
    const text = generateShareText();
    const url = getCurrentUrl();
    const message = `${text} ${url}`;

    const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
  };

  // Share to LINE
  const shareToLine = () => {
    const text = generateShareText();
    const url = getCurrentUrl();

    const lineUrl = `https://social-plugins.line.me/lineit/share?url=${encodeURIComponent(url)}&text=${encodeURIComponent(text)}`;
    window.open(lineUrl, '_blank');
  };

  // Share to Reddit
  const shareToReddit = () => {
    const text = generateShareText();
    const url = getCurrentUrl();
    const title = `Check out this awesome Sybau meme I created! 🔥`;

    const redditUrl = `https://www.reddit.com/submit?url=${encodeURIComponent(url)}&title=${encodeURIComponent(title)}&text=${encodeURIComponent(text)}`;
    window.open(redditUrl, '_blank');
  };

  // Share to Telegram
  const shareToTelegram = () => {
    const text = generateShareText();
    const url = getCurrentUrl();

    const telegramUrl = `https://t.me/share/url?url=${encodeURIComponent(url)}&text=${encodeURIComponent(text)}`;
    window.open(telegramUrl, '_blank');
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, scale: 0.8 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 20
      }
    }
  };

  return (
    <motion.div
      className={`flex justify-center space-x-2 flex-wrap gap-y-2 ${className}`}
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <motion.div variants={itemVariants}>
        <FacebookShareButton
          url={finalShareUrl}
          hashtag="#meme"
          onClick={handleShare}
        >
          <FacebookIcon size={iconSize} round />
        </FacebookShareButton>
      </motion.div>

      <motion.div variants={itemVariants}>
        <TwitterShareButton
          url={finalShareUrl}
          title={shareTextWithImage}
          onClick={handleShare}
        >
          <TwitterIcon size={iconSize} round />
        </TwitterShareButton>
      </motion.div>

      <motion.div variants={itemVariants}>
        <RedditShareButton
          url={finalShareUrl}
          title={shareTextWithImage}
          onClick={handleShare}
        >
          <RedditIcon size={iconSize} round />
        </RedditShareButton>
      </motion.div>

      <motion.div variants={itemVariants}>
        <WhatsappShareButton
          url={finalShareUrl}
          title={shareTextWithImage}
          onClick={handleShare}
        >
          <WhatsappIcon size={iconSize} round />
        </WhatsappShareButton>
      </motion.div>

      <motion.div variants={itemVariants}>
        <TelegramShareButton
          url={finalShareUrl}
          title={shareTextWithImage}
          onClick={handleShare}
        >
          <TelegramIcon size={iconSize} round />
        </TelegramShareButton>
      </motion.div>

      <motion.div variants={itemVariants}>
        <WeiboShareButton
          url={finalShareUrl}
          title={shareTextWithImage}
          onClick={handleShare}
        >
          <WeiboIcon size={iconSize} round />
        </WeiboShareButton>
      </motion.div>
    </motion.div>
  );
}
