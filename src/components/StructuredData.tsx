import Script from 'next/script';
import { siteConfig } from '@/data/site-config';

interface StructuredDataProps {
  type: 'website' | 'article' | 'faq' | 'howto' | 'organization';
  data?: {
    questions?: Array<{ question: string; answer: string }>;
    title?: string;
    description?: string;
    url?: string;
    datePublished?: string;
    dateModified?: string;
    keywords?: string[];
    image?: string;
    totalTime?: string;
    supplies?: Array<{ name: string }>;
    steps?: Array<{ name: string; text: string; url: string }>;
  };
}

export default function StructuredData({ type, data }: StructuredDataProps) {
  const getStructuredData = () => {
    const baseData = {
      "@context": "https://schema.org",
      "@type": type === 'website' ? 'WebSite' : type === 'article' ? 'Article' : type === 'faq' ? 'FAQPage' : type === 'howto' ? 'HowTo' : 'Organization'
    };

    switch (type) {
      case 'website':
        return {
          ...baseData,
          "@type": "WebSite",
          "name": siteConfig.name,
          "description": siteConfig.description,
          "url": siteConfig.url,
          "potentialAction": {
            "@type": "SearchAction",
            "target": {
              "@type": "EntryPoint",
              "urlTemplate": `${siteConfig.url}/?search={search_term_string}`
            },
            "query-input": "required name=search_term_string"
          },
          "publisher": {
            "@type": "Organization",
            "name": "Sybau Meme Generator",
            "url": siteConfig.url
          }
        };

      case 'article':
        return {
          ...baseData,
          "@type": "Article",
          "headline": data?.title || "Sybau Meme Creation Guide",
          "description": data?.description || "Learn how to create viral sybau memes",
          "author": {
            "@type": "Organization",
            "name": "Sybau Meme Generator Team"
          },
          "publisher": {
            "@type": "Organization",
            "name": "Sybau Meme Generator",
            "url": siteConfig.url
          },
          "datePublished": data?.datePublished || new Date().toISOString(),
          "dateModified": data?.dateModified || new Date().toISOString(),
          "mainEntityOfPage": {
            "@type": "WebPage",
            "@id": data?.url || siteConfig.url
          },
          "articleSection": "Meme Creation",
          "keywords": data?.keywords || ["sybau meme", "meme generator", "viral memes", "meme creator"]
        };

      case 'faq':
        return {
          ...baseData,
          "@type": "FAQPage",
          "mainEntity": data?.questions?.map((q) => ({
            "@type": "Question",
            "name": q.question,
            "acceptedAnswer": {
              "@type": "Answer",
              "text": q.answer
            }
          })) || []
        };

      case 'howto':
        return {
          ...baseData,
          "@type": "HowTo",
          "name": data?.title || "How to Create Sybau Memes",
          "description": data?.description || "Step-by-step guide to creating viral sybau memes",
          "image": data?.image || `${siteConfig.url}/og.png`,
          "totalTime": data?.totalTime || "PT5M",
          "estimatedCost": {
            "@type": "MonetaryAmount",
            "currency": "USD",
            "value": "0"
          },
          "supply": data?.supplies || [
            {
              "@type": "HowToSupply",
              "name": "Computer or mobile device"
            },
            {
              "@type": "HowToSupply", 
              "name": "Internet connection"
            }
          ],
          "tool": [
            {
              "@type": "HowToTool",
              "name": "Sybau Meme Generator"
            }
          ],
          "step": data?.steps || [
            {
              "@type": "HowToStep",
              "name": "Choose Template",
              "text": "Select a meme template from our collection",
              "url": `${siteConfig.url}/#templates`
            },
            {
              "@type": "HowToStep",
              "name": "Add Text",
              "text": "Add your custom text to the template",
              "url": `${siteConfig.url}/guide#text-techniques`
            },
            {
              "@type": "HowToStep",
              "name": "Download",
              "text": "Download your completed meme",
              "url": `${siteConfig.url}/guide#sharing-strategies`
            }
          ]
        };

      case 'organization':
        return {
          ...baseData,
          "@type": "Organization",
          "name": "Sybau Meme Generator",
          "description": siteConfig.description,
          "url": siteConfig.url,
          "logo": `${siteConfig.url}/logo.png`,
          "sameAs": [
            // Add social media URLs when available
          ],
          "contactPoint": {
            "@type": "ContactPoint",
            "contactType": "customer service",
            "email": "<EMAIL>",
            "url": `${siteConfig.url}/contact`
          },
          "foundingDate": "2024",
          "founder": {
            "@type": "Organization",
            "name": "Sybau Meme Generator Team"
          },
          "knowsAbout": [
            "Meme Creation",
            "Digital Content",
            "Social Media",
            "Viral Marketing",
            "Community Building"
          ],
          "serviceType": "Meme Generator",
          "areaServed": "Worldwide"
        };

      default:
        return baseData;
    }
  };

  const structuredData = getStructuredData();

  return (
    <Script
      id={`structured-data-${type}`}
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(structuredData),
      }}
    />
  );
}
