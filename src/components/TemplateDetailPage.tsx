"use client";

import React from 'react';
import { Template } from '@/types/template';
import DynamicMemeEditor from './DynamicMemeEditor';
import FAQ from './FAQ';
import RelatedTemplates from './RelatedTemplates';
import { siteConfig } from '@/data/site-config';
import Script from 'next/script';

type TemplateDetailPageProps = {
  templateKey: string;
  template: Template;
};

export default function TemplateDetailPage({ templateKey, template }: TemplateDetailPageProps) {
  const templateName = templateKey.replace(/-/g, ' ');
  const capitalizedTemplateName = templateName.split(' ').map(word =>
    word.charAt(0).toUpperCase() + word.slice(1)
  ).join(' ');

  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": `${capitalizedTemplateName} Sybau Meme Generator`,
    "description": `Create hilarious ${templateName} sybau memes instantly with our free online meme generator. Customize text and create viral ${templateName} content.`,
    "url": `${siteConfig.url}/sybau-meme-template/${templateKey}`,
    "mainEntity": {
      "@type": "SoftwareApplication",
      "name": `${capitalizedTemplateName} Meme Generator`,
      "applicationCategory": "EntertainmentApplication",
      "operatingSystem": "Any",
      "offers": {
        "@type": "Offer",
        "price": "0",
        "priceCurrency": "USD"
      },
      "featureList": [
        `Create ${templateName} memes`,
        "Custom text editing",
        "Instant download",
        "Free to use",
        "No registration required"
      ]
    },
    "breadcrumb": {
      "@type": "BreadcrumbList",
      "itemListElement": [
        {
          "@type": "ListItem",
          "position": 1,
          "name": "Home",
          "item": siteConfig.url
        },
        {
          "@type": "ListItem",
          "position": 2,
          "name": `${capitalizedTemplateName} Meme Generator`,
          "item": `${siteConfig.url}/sybau-meme-template/${templateKey}`
        }
      ]
    }
  };

  return (
    <>
      <Script
        id="template-structured-data"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData),
        }}
      />
      <div className="max-w-7xl mx-auto py-8">

      {/* 页面标题 */}
      <div className="mb-8 text-center">
        <h1 className="text-4xl md:text-5xl font-bold mb-4 capitalize">
          {templateName} <span className="sybau-text-gradient">Sybau Meme Generator</span> 
        </h1>
        <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
          Create hilarious {templateName} memes instantly with our exclusive sybau meme generator.
          Customize the text, add your personal touch, and create viral content that stands out!
        </p>
        <div className="flex flex-wrap justify-center gap-4 mt-4 text-sm text-muted-foreground">
          <span className="flex items-center gap-1">
            🔥 <strong>Viral Templates</strong>
          </span>
          <span className="flex items-center gap-1">
            ⚡ <strong>Instant Creation</strong>
          </span>
          <span className="flex items-center gap-1">
            📱 <strong>Mobile Ready</strong>
          </span>
        </div>
      </div>

      {/* 内联Meme编辑器 */}
      <div className="mb-12">
        <div className="bg-gradient-to-br from-accent/10 to-primary/5 rounded-2xl p-6 sybau-border">
          <div className="mb-6 text-center">
            <h2 className="text-2xl font-semibold mb-2">
              Create Your <span className="sybau-text-gradient">{templateName}</span> Meme
            </h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Use our powerful sybau meme editor to customize your content. Add text, adjust positioning,
              and create viral-worthy memes that capture attention!
            </p>
          </div>

          <div className="sybau-glow rounded-xl overflow-hidden">
            <DynamicMemeEditor
              template={template}
              showBackButton={false}
            />
          </div>
        </div>
      </div>

      {/* SEO内容区域 */}
      <div className="space-y-8">
        <section>
          <h2 className="text-3xl font-bold mb-4">
            About <span className="sybau-text-gradient">{templateName}</span> Memes
          </h2>
          <div className="prose dark:prose-invert max-w-none">
            <p className="text-lg leading-relaxed">
              The {templateName} meme template is one of the most popular formats in the sybau meme universe for creating viral content online.
              This versatile template allows you to express humor, reactions, and commentary in a format that resonates
              with internet culture and the sybau community.
            </p>
            <p>
              Our exclusive sybau {templateName} meme generator makes it easy to create your own version of this popular meme.
              Simply add your custom text, adjust the positioning, and download your creation to share across social media platforms.
              Join thousands of creators who use sybau memes to engage their audience!
            </p>
          </div>
        </section>

        <section>
          <h2 className="text-3xl font-bold mb-4">How to Create {templateName} Memes</h2>
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-xl font-semibold mb-3">Step-by-Step Guide:</h3>
              <ol className="space-y-2 text-muted-foreground">
                <li>1. Add your custom text to the text boxes</li>
                <li>2. Adjust font size and positioning as needed</li>
                <li>3. Preview your meme creation</li>
                <li>4. Download or share your finished meme</li>
              </ol>
            </div>
            <div>
              <h3 className="text-xl font-semibold mb-3">Pro Tips:</h3>
              <ul className="space-y-2 text-muted-foreground">
                <li>• Keep text concise and impactful</li>
                <li>• Use contrasting colors for readability</li>
                <li>• Consider your audience and context</li>
                <li>• Test different text sizes for mobile viewing</li>
              </ul>
            </div>
          </div>
        </section>
      </div>

      {/* 相关模板推荐 */}
      <div className="mb-12">
        <RelatedTemplates
          currentTemplateKey={templateKey}
          maxItems={8}
        />
      </div>

      {/* FAQ Section */}
      <FAQ
        title={`Sybau ${templateName} Meme Generator FAQ`}
        subtitle={`Common questions about creating viral ${templateName} memes with sybau style`}
        maxItems={6}
        templateSpecific={templateName}
      />
    </div>
    </>
  );
}
