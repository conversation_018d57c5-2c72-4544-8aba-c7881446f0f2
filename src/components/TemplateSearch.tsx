"use client"

import { useState } from "react";
import { ArrowDownRight, Upload } from "lucide-react";
import { templates } from "@/data/templates";
import { Template } from "@/types/template";
import MainContainer from "./MainContainer";
import { motion } from "framer-motion";
import useSelected from "@/hooks/useSelected";
import CustomTemplateUpload from "./CustomTemplateUpload";
import { useRouter } from "next/navigation";

export default function TemplateSearch() {
    const [searchQuery, setSearchQuery] = useState("");
    const { selected, handleCustomTemplateSelect } = useSelected();
    const router = useRouter();

    // 自定义模板上传成功后的处理函数
    const handleCustomTemplateCreate = (template: Template) => {
        // 先设置自定义模板
        handleCustomTemplateSelect(template);
        // 直接跳转，不使用查询参数
        router.replace('/meme-generator');
    };

    const filteredTemplates = Object.entries(templates).filter(([key]) =>
        key.toLowerCase().replace(/-/g, ' ').includes(searchQuery.toLowerCase())
    );

    return (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, ease: [0.22, 1, 0.36, 1] }}
        >
            <div className={`justify-center pb-16 relative w-full ${selected ? 'hidden' : 'flex'}`}>
                <div className="flex items-center gap-4 w-full max-w-2xl">
                    <motion.div
                        className="relative flex-1 max-w-md"
                        whileFocus={{ scale: 1.02 }}
                        transition={{ duration: 0.2 }}
                    >
                        <motion.input
                            type="text"
                            placeholder="Search template"
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                            className="rounded-3xl text-sm py-2 pl-4 pr-10 w-full bg-[#0f0f0f] border border-white/20 text-white focus:outline-none focus:ring-2 focus:ring-[#6a7bd1] transition"
                            style={{
                                boxShadow: "0 0 0 2px rgba(106,123,209,0.3), 0 4px 24px 0px rgba(106,123,209,0.5), 0 0 0 0 transparent",
                            }}
                            whileFocus={{
                                boxShadow: "0 0 0 2px rgba(106,123,209,0.5), 0 4px 24px 0px rgba(106,123,209,0.7), 0 0 0 0 transparent",
                            }}
                        />
                        <motion.span
                            className="bg-white rounded-full p-1 text-black absolute right-2 top-1/2 -translate-y-1/2 flex items-center justify-center shadow-md"
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.95 }}
                        >
                            <ArrowDownRight className="w-4 h-4" />
                        </motion.span>
                    </motion.div>

                    {/* Custom Template Upload Button */}
                    <motion.div
                        initial={{ opacity: 0, x: 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.3, delay: 0.1 }}
                    >
                        <CustomTemplateUpload
                            onTemplateCreate={handleCustomTemplateCreate}
                            buttonText="Custom"
                            buttonIcon={<Upload className="h-4 w-4" />}
                            buttonClassName="py-2 px-4 h-10 rounded-2xl bg-primary text-primary-foreground hover:bg-primary/90 transition-colors flex items-center gap-2 text-sm font-semibold sybau-glow"
                            title="Upload Custom Template"
                            description="Upload your own image to create a custom meme template."
                        />
                    </motion.div>
                </div>
            </div>
            {
                filteredTemplates.length < 1 ? <div className="min-h-[30vh] max-sm:min-h-[50vh]"><p className="text-center">No templates found</p></div> : <MainContainer templates={Object.fromEntries(filteredTemplates)} />
            }
        </motion.div>
    );
} 