'use client';

import React, { useState, useMemo, useRef, useEffect } from 'react';
import { Template } from '@/types/template';
import Image from 'next/image';
import Link from 'next/link';
import { motion, AnimatePresence } from 'framer-motion';
import {
    Pagination,
    PaginationContent,
    PaginationEllipsis,
    PaginationItem,
    PaginationLink,
    PaginationNext,
    PaginationPrevious,
} from "@/components/ui/pagination";
import useSelected from '@/hooks/useSelected';
import { createSafeSlug } from '@/lib/template-utils';

type TemplateSelectorProps = {
    templates: Record<string, Template>;
};

const TEMPLATES_PER_PAGE = 60;
const PRELOAD_NEXT_PAGE = true;

export default function TemplateSelector({ templates }: TemplateSelectorProps) {
    const { currentPage, setCurrentPage } = useSelected();
    const [loadedImages, setLoadedImages] = useState<Set<string>>(new Set());
    const intersectionObserverRef = useRef<IntersectionObserver | null>(null);
    const imageRefs = useRef<Map<string, HTMLElement>>(new Map());

    const templateEntries = useMemo(() => Object.entries(templates), [templates]);
    const totalPages = Math.ceil(templateEntries.length / TEMPLATES_PER_PAGE);

    const paginatedTemplates = useMemo(() => {
        const startIndex = (currentPage - 1) * TEMPLATES_PER_PAGE;
        const endIndex = startIndex + TEMPLATES_PER_PAGE;
        return templateEntries.slice(startIndex, endIndex);
    }, [templateEntries, currentPage]);

    const nextPageTemplates = useMemo(() => {
        if (!PRELOAD_NEXT_PAGE || currentPage >= totalPages) return [];
        const startIndex = currentPage * TEMPLATES_PER_PAGE;
        const endIndex = startIndex + TEMPLATES_PER_PAGE;
        return templateEntries.slice(startIndex, endIndex);
    }, [templateEntries, currentPage, totalPages]);

    useEffect(() => {
        if (currentPage > totalPages && totalPages > 0) {
            setCurrentPage(1);
        }
    }, [totalPages, currentPage, setCurrentPage]);

    useEffect(() => {
        if (nextPageTemplates.length > 0) {
            nextPageTemplates.forEach(([, template]) => {
                const link = document.createElement('link');
                link.rel = 'preload';
                link.as = 'image';
                link.href = template.image;
                link.crossOrigin = 'anonymous';
                document.head.appendChild(link);

                setTimeout(() => {
                    if (document.head.contains(link)) {
                        document.head.removeChild(link);
                    }
                }, 30000);
            });
        }
    }, [nextPageTemplates]);

    useEffect(() => {
        intersectionObserverRef.current = new IntersectionObserver(
            (entries) => {
                entries.forEach((entry) => {
                    if (entry.isIntersecting) {
                        const img = entry.target as HTMLElement;
                        const imageSrc = img.dataset.src;
                        if (imageSrc && !loadedImages.has(imageSrc)) {
                            setLoadedImages(prev => new Set([...prev, imageSrc]));
                        }
                    }
                });
            },
            {
                rootMargin: '50px',
                threshold: 0.1,
            }
        );

        return () => {
            intersectionObserverRef.current?.disconnect();
        };
    }, [loadedImages]);

    useEffect(() => {
        const observer = intersectionObserverRef.current;
        if (!observer) return;

        const currentImageRefs = imageRefs.current;

        currentImageRefs.forEach((element) => {
            observer.observe(element);
        });

        return () => {
            currentImageRefs.forEach((element) => {
                observer.unobserve(element);
            });
        };
    }, [paginatedTemplates]);

    const handlePageChange = (page: number) => {
        setCurrentPage(page);
        window.scrollTo({ top: 0, behavior: 'smooth' });
    };

    const setImageRef = (key: string) => (element: HTMLElement | null) => {
        if (element) {
            imageRefs.current.set(key, element);
            element.dataset.src = templates[key]?.image;
        } else {
            imageRefs.current.delete(key);
        }
    };

    const renderPaginationItems = () => {
        const items = [];

        if (currentPage > 3) {
            items.push(
                <PaginationItem key={1}>
                    <PaginationLink
                        onClick={() => handlePageChange(1)}
                        isActive={currentPage === 1}
                    >
                        1
                    </PaginationLink>
                </PaginationItem>
            );

            if (currentPage > 4) {
                items.push(
                    <PaginationItem key="ellipsis-start">
                        <PaginationEllipsis />
                    </PaginationItem>
                );
            }
        }

        const startPage = Math.max(1, currentPage - 2);
        const endPage = Math.min(totalPages, currentPage + 2);

        for (let page = startPage; page <= endPage; page++) {
            items.push(
                <PaginationItem key={page}>
                    <PaginationLink
                        onClick={() => handlePageChange(page)}
                        isActive={currentPage === page}
                    >
                        {page}
                    </PaginationLink>
                </PaginationItem>
            );
        }

        if (currentPage < totalPages - 2) {
            if (currentPage < totalPages - 3) {
                items.push(
                    <PaginationItem key="ellipsis-end">
                        <PaginationEllipsis />
                    </PaginationItem>
                );
            }

            items.push(
                <PaginationItem key={totalPages}>
                    <PaginationLink
                        onClick={() => handlePageChange(totalPages)}
                        isActive={currentPage === totalPages}
                    >
                        {totalPages}
                    </PaginationLink>
                </PaginationItem>
            );
        }

        return items;
    };

    return (
        <div className="space-y-6 w-full">
            {/* Templates Grid */}
            <section className="grid grid-cols-6 max-sm:grid-cols-2 max-md:grid-cols-3 max-lg:grid-cols-4 gap-6 grid-flow-dense w-full max-sm:-mt-3">
                <AnimatePresence mode="popLayout">
                    {paginatedTemplates.map(([key, tpl], index) => {
                        const isPriority = index < 6 && currentPage === 1;
                        return (
                            <motion.div
                                key={key}
                                ref={setImageRef(key)}
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                exit={{ opacity: 0, y: -20 }}
                                transition={{
                                    duration: 0.3,
                                    delay: index * 0.05,
                                    ease: [0.22, 1, 0.36, 1]
                                }}
                                className="group"
                            >
                                <Link href={`/sybau-meme-template/${createSafeSlug(key)}`}>
                                    <motion.div
                                        className="relative aspect-square cursor-pointer"
                                        whileHover={{ scale: 1.02 }}
                                        transition={{ duration: 0.2 }}
                                    >
                                    <Image
                                        src={tpl.image}
                                        alt={key}
                                        fill
                                        className="object-cover rounded-2xl shadow transition-opacity duration-300"
                                        loading={isPriority ? 'eager' : 'lazy'}
                                        priority={isPriority}
                                        quality={100}
                                        sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 16.67vw"
                                        placeholder="blur"
                                        blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q=="
                                        onLoad={() => {
                                            setLoadedImages(prev => new Set([...prev, tpl.image]));
                                        }}
                                    />
                                    {/* Overlay with actions */}
                                    <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 rounded-2xl flex items-center justify-center">
                                        <div className="flex gap-2">
                                            <div className="bg-primary text-primary-foreground px-3 py-1 rounded-md text-sm font-medium hover:bg-primary/90 transition-colors flex items-center gap-1">
                                                Create Meme
                                            </div>
                                        </div>
                                    </div>
                                </motion.div>
                                    <motion.div
                                        className="text-center mt-2"
                                        initial={{ opacity: 0 }}
                                        animate={{ opacity: 1 }}
                                        transition={{ delay: 0.1 }}
                                    >
                                        <p className="text-base font-medium capitalize">
                                            {key.replace(/-/g, ' ')}
                                        </p>
                                    </motion.div>
                                </Link>
                            </motion.div>
                        );
                    })}
                </AnimatePresence>
            </section>

            {/* Pagination Controls */}
            {totalPages > 1 && (
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: 0.2 }}
                    className="flex flex-col items-center pt-6 space-y-4"
                >

                    <Pagination>
                        <PaginationContent>
                            <PaginationItem>
                                <PaginationPrevious
                                    onClick={() => currentPage > 1 && handlePageChange(currentPage - 1)}
                                    className={currentPage === 1 ? 'opacity-50 cursor-not-allowed' : ''}
                                />
                            </PaginationItem>

                            {renderPaginationItems()}

                            <PaginationItem>
                                <PaginationNext
                                    onClick={() => currentPage < totalPages && handlePageChange(currentPage + 1)}
                                    className={currentPage === totalPages ? 'opacity-50 cursor-not-allowed' : ''}
                                />
                            </PaginationItem>
                        </PaginationContent>
                    </Pagination>
                </motion.div>
            )}
        </div>
    );
}