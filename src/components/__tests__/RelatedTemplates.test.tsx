import React from 'react';
import { render, screen } from '@testing-library/react';
import RelatedTemplates from '../RelatedTemplates';

// Mock Next.js components
jest.mock('next/image', () => {
  return function MockImage({ alt, ...props }: React.ImgHTMLAttributes<HTMLImageElement> & { alt: string }) {
    return <img alt={alt} {...props} />;
  };
});

jest.mock('next/link', () => {
  return function MockLink({ children, href, ...props }: React.AnchorHTMLAttributes<HTMLAnchorElement> & { children: React.ReactNode; href: string }) {
    return <a href={href} {...props}>{children}</a>;
  };
});

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: React.HTMLAttributes<HTMLDivElement> & { children?: React.ReactNode }) => <div {...props}>{children}</div>,
  },
}));

describe('RelatedTemplates', () => {
  it('renders related templates section', () => {
    render(<RelatedTemplates currentTemplateKey="Scared-Lady" maxItems={4} />);
    
    expect(screen.getByText('Other Popular Templates')).toBeInTheDocument();
    expect(screen.getByText('Explore more popular meme templates to create even more hilarious content')).toBeInTheDocument();
    expect(screen.getByText('Browse All Templates')).toBeInTheDocument();
  });

  it('excludes current template from recommendations', () => {
    render(<RelatedTemplates currentTemplateKey="Scared-Lady" maxItems={8} />);
    
    // Should not show the current template
    const scaredLadyLinks = screen.queryAllByText(/scared lady/i);
    expect(scaredLadyLinks).toHaveLength(0);
  });

  it('limits the number of templates shown', () => {
    render(<RelatedTemplates currentTemplateKey="Scared-Lady" maxItems={4} />);
    
    // Should show at most 4 templates (excluding current)
    const templateImages = screen.getAllByRole('img');
    expect(templateImages.length).toBeLessThanOrEqual(4);
  });
});
