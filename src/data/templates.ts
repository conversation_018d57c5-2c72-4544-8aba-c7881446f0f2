export const templates = {
    // Sybau 相关模板 - 优先显示
    "sybau template": {
        image: "/webp/temp2.webp",
        textBoxes: [
            { x: 60, y: 70, width: 600, height: 400, fontSize: 70, minFont: 40, align: "center" as const }
        ]
    },
    "Empty": {
        image: "/webp/temp9.webp",
        textBoxes: [
            { x: 60, y: 70, width: 600, height: 400, fontSize: 70, minFont: 40, align: "center" as const }
        ]
    },

    // 其他模板
    "Scared-Lady": {
        image: "/webp/temp1.webp",
        textBoxes: [
            { x: 100, y: 370, width: 600, height: 400, fontSize: 100, minFont: 40, align: "center" as const },
            { x: 700, y: 750, width: 600, height: 400, fontSize: 100, minFont: 40, align: "center" as const }
        ]
    },
    "Harkirat-brah": {
        image: "/webp/temp10.webp",
        textBoxes: [
            { x: 11, y: 58, width: 700, height: 100, fontSize: 40, minFont: 20, align: "center" as const },
            { x: 9, y: 425, width: 700, height: 100, fontSize: 40, minFont: 20, align: "center" as const },
        ]
    },

    "Modi-g-Poster": {
        image: "/webp/temp27.webp",
        textBoxes: [
            { x: 100, y: 170, width: 450, height: 400, fontSize: 70, minFont: 20, align: "center" as const },
            { x: 100, y: 470, width: 450, height: 400, fontSize: 70, minFont: 20, align: "center" as const },
        ],
    },

    "Amrit-dawg": {
        image: "/webp/temp30.webp",
        textBoxes: [
            { x: 100, y: 170, width: 1800, height: 400, fontSize: 150, minFont: 20, align: "center" as const },
            { x: 100, y: 970, width: 1800, height: 400, fontSize: 150, minFont: 20, align: "center" as const },
        ],
    },

    "only-aim": {
        image: "/webp/temp54.webp",
        textBoxes: [
            { x: 350, y: 950, width: 2400, height: 1000, fontSize: 200, minFont: 40, align: "center" as const },
            { x: 1050, y: 2750, width: 800, height: 1000, fontSize: 250, minFont: 40, align: "center" as const }
        ]
    },

    "no-way-dawg": {
        image: "/webp/temp57.webp",
        textBoxes: [
            { x: 150, y: 1350, width: 1400, height: 600, fontSize: 200, minFont: 40, align: "center" as const },
            { x: 2000, y: 1250, width: 1100, height: 800, fontSize: 200, minFont: 40, align: "center" as const }
        ]
    },

    "Kirat-humble-guy": {
        image: "/webp/temp18.webp",
        textBoxes: [
            { x: 5, y: 50, width: 470, height: 100, fontSize: 40, minFont: 10, align: "center" as const },
            { x: 5, y: 395, width: 470, height: 90, fontSize: 40, minFont: 10, align: "center" as const },
        ]
    },

    "Bumrah-Bhai": {
        image: "/webp/temp26.webp",
        textBoxes: [
            { x: 100, y: 470, width: 450, height: 400, fontSize: 70, minFont: 20, align: "center" as const },
            { x: 530, y: 570, width: 400, height: 400, fontSize: 70, minFont: 20, align: "center" as const },
        ],
    },

    "Pani-pilo": {
        image: "/webp/temp33.webp",
        textBoxes: [
            { x: 10, y: 60, width: 600, height: 100, fontSize: 60, minFont: 20, align: "center" as const },
            { x: 10, y: 450, width: 600, height: 100, fontSize: 60, minFont: 20, align: "center" as const },
        ],
    },

    "the-great-khali": {
        image: "/webp/temp37.webp",
        textBoxes: [
            { x: 1, y: 60, width: 640, height: 300, fontSize: 50, minFont: 20, align: "center" as const },
            { x: 10, y: 540, width: 630, height: 140, fontSize: 50, minFont: 20, align: "center" as const },
        ]
    },

    "wait-a-minute": {
        image: "/webp/temp49.webp",
        textBoxes: [
            { x: 1, y: 450, width: 1200, height: 600, fontSize: 200, minFont: 40, align: "center" as const },
            { x: 50, y: 1450, width: 1200, height: 600, fontSize: 200, minFont: 40, align: "center" as const }
        ]
    },

    "two-spiders": {
        image: "/webp/temp61.webp",
        textBoxes: [
            { x: 100, y: 600, width: 800, height: 600, fontSize: 150, minFont: 40, align: "center" as const },
            { x: 1100, y: 600, width: 800, height: 600, fontSize: 150, minFont: 40, align: "center" as const },
        ]
    },

    "two-paths": {
        image: "/webp/temp51.webp",
        textBoxes: [
            { x: 10, y: 200, width: 1000, height: 400, fontSize: 150, minFont: 40, align: "center" as const },
            { x: 1000, y: 200, width: 1000, height: 400, fontSize: 150, minFont: 40, align: "center" as const },
        ]
    },

    "secret-revealed": {
        image: "/webp/temp59.webp",
        textBoxes: [
            { x: 100, y: 450, width: 2000, height: 1200, fontSize: 300, minFont: 40, align: "center" as const },
            { x: 100, y: 3325, width: 2000, height: 1200, fontSize: 300, minFont: 40, align: "center" as const },
        ]
    },

    "chess-tension": {
        image: "/webp/temp36.webp",
        textBoxes: [
            { x: 20, y: 60, width: 640, height: 200, fontSize: 50, minFont: 20, align: "center" as const },
            { x: 20, y: 450, width: 630, height: 200, fontSize: 50, minFont: 20, align: "center" as const },
        ]
    },

    "listen-to-me": {
        image: "/webp/temp35.webp",
        textBoxes: [
            { x: 1, y: 260, width: 500, height: 300, fontSize: 80, minFont: 20, align: "center" as const },
            { x: 450, y: 560, width: 400, height: 300, fontSize: 80, minFont: 20, align: "center" as const },
        ]
    },

    "wanna-be": {
        image: "/webp/temp34.webp",
        textBoxes: [
            { x: 1, y: 260, width: 300, height: 200, fontSize: 40, minFont: 20, align: "center" as const },
            { x: 400, y: 260, width: 300, height: 200, fontSize: 40, minFont: 20, align: "center" as const },
        ]
    },

    "you-vs-him": {
        image: "/webp/temp44.webp",
        textBoxes: [
            { x: 80, y: 450, width: 1000, height: 500, fontSize: 140, minFont: 10, align: "center" as const },
            { x: 40, y: 1500, width: 500, height: 400, fontSize: 140, minFont: 10, align: "center" as const },
            { x: 480, y: 1500, width: 500, height: 400, fontSize: 140, minFont: 10, align: "center" as const },
        ]
    },

    "developer-stage": {
        image: "/webp/temp47.webp",
        textBoxes: [
            { x: 900, y: 150, width: 600, height: 400, fontSize: 100, minFont: 40, align: "center" as const },
            { x: 900, y: 610, width: 600, height: 400, fontSize: 100, minFont: 40, align: "center" as const },
            { x: 900, y: 1050, width: 600, height: 400, fontSize: 100, minFont: 40, align: "center" as const },
            { x: 900, y: 1500, width: 600, height: 400, fontSize: 100, minFont: 40, align: "center" as const }
        ]
    },

    "Bhola-Bachcha": {
        image: "/webp/temp16.webp",
        textBoxes: [
            { x: 50, y: 240, width: 2000, height: 500, fontSize: 180, minFont: 40, align: "center" as const },
            { x: 50, y: 1880, width: 2000, height: 300, fontSize: 180, minFont: 40, align: "center" as const },
        ]
    },

    "hungery-monkey": {
        image: "/webp/temp28.webp",
        textBoxes: [
            { x: 240, y: 450, width: 450, height: 100, fontSize: 60, minFont: 40, align: "center" as const },
            { x: 20, y: 850, width: 400, height: 100, fontSize: 60, minFont: 40, align: "center" as const },
        ],
    },

    "sympathy-girl": {
        image: "/webp/temp20.webp",
        textBoxes: [
            { x: 50, y: 1000, width: 1150, height: 200, fontSize: 100, minFont: 50, align: "center" as const },
        ]
    },

    "Doge Vs. Cheems": {
        image: "/webp/temp22.webp",
        textBoxes: [
            { id: "topText", x: 50, y: 400, width: 400, height: 200, fontSize: 60, minFont: 14, align: "center" as const },
            { id: "handText", x: 600, y: 400, width: 400, height: 200, fontSize: 60, minFont: 14, align: "center" as const },
        ],
    },

    "I Bet He's Thinking About Other Women": {
        image: "/webp/temp23.webp",
        textBoxes: [
            { x: 100, y: 470, width: 600, height: 400, fontSize: 100, minFont: 40, align: "center" as const },
            { x: 800, y: 650, width: 600, height: 400, fontSize: 100, minFont: 40, align: "center" as const }
        ],
    },

    "aaja-bhidle": {
        image: "/webp/temp19.webp",
        textBoxes: [
            { x: 0, y: 550, width: 480, height: 100, fontSize: 40, minFont: 10, align: "center" as const },
        ]
    },

    "flexing-on-baddies": {
        image: "/webp/temp29.webp",
        textBoxes: [
            { x: 20, y: 60, width: 650, height: 100, fontSize: 60, minFont: 20, align: "center" as const },
            { x: 20, y: 450, width: 650, height: 100, fontSize: 60, minFont: 20, align: "center" as const },
        ],
    },

    "distracted-boyfriend": {
        image: "/webp/temp8.webp",
        textBoxes: [
            { x: 80, y: 550, width: 520, height: 150, fontSize: 80, minFont: 40, align: "center" as const },
            { x: 560, y: 400, width: 400, height: 100, fontSize: 70, minFont: 40, align: "center" as const },
            { x: 800, y: 500, width: 400, height: 200, fontSize: 70, minFont: 40, align: "center" as const }
        ]
    },

    "Ch*d gaye guru": {
        image: "/webp/temp24.webp",
        textBoxes: [
            { x: 100, y: 470, width: 300, height: 100, fontSize: 40, minFont: 20, align: "center" as const },
        ],
    },

    "Buzz-And-Woody": {
        image: "/webp/temp11.webp",
        textBoxes: [
            { id: "topText", x: 25, y: 60, width: 700, height: 100, fontSize: 56, minFont: 10, align: "center" as const },
            { id: "handText", x: 25, y: 445, width: 700, height: 100, fontSize: 56, minFont: 10, align: "center" as const },
        ],
    },

    "Grim-Reaper-Doors": {
        image: "/webp/temp7.webp",
        textBoxes: [
            { id: 'door-1', x: 58, y: 1050, width: 508, height: 300, fontSize: 100, minFont: 40, align: 'center' as const },
            { id: 'door-2', x: 708, y: 1300, width: 630, height: 400, fontSize: 150, minFont: 40, align: 'center' as const },
            { id: 'door-3', x: 1758, y: 1244, width: 658, height: 300, fontSize: 200, minFont: 40, align: 'center' as const },
            { id: 'door-4', x: 3290, y: 1320, width: 958, height: 400, fontSize: 250, minFont: 40, align: 'center' as const },
            { id: 'reaper-body', x: 2600, y: 1880, width: 710, height: 400, fontSize: 300, minFont: 40, align: 'center' as const },
        ],
    },

    "Disaster-Girl": {
        image: "/webp/temp21.webp",
        textBoxes: [
            { id: "topText", x: 10, y: 140, width: 180, height: 200, fontSize: 40, minFont: 14, align: "center" as const },
            { id: "handText", x: 200, y: 350, width: 180, height: 200, fontSize: 40, minFont: 14, align: "center" as const },
        ],
    },

    "IQ-Curve": {
        image: "/webp/temp38.webp",
        textBoxes: [
            { x: 50, y: 850, width: 850, height: 480, fontSize: 180, minFont: 40, align: "center" as const },
            { x: 700, y: 200, width: 1300, height: 270, fontSize: 150, minFont: 0, align: "center" as const },
            { x: 1900, y: 700, width: 850, height: 480, fontSize: 180, minFont: 40, align: "center" as const },
        ],
    },

    "black-&-white-woolfs": {
        image: "/webp/temp43.webp",
        textBoxes: [
            { x: 100, y: 1850, width: 2600, height: 1600, fontSize: 400, minFont: 40, align: "center" as const },
            { x: 3000, y: 1850, width: 2600, height: 1600, fontSize: 400, minFont: 40, align: "center" as const }
        ]
    },

    "haha-im-death": {
        image: "/webp/temp60.webp",
        textBoxes: [
            { x: 750, y: 850, width: 900, height: 600, fontSize: 160, minFont: 40, align: "center" as const },
        ]
    },

    "he-dont-know-yet": {
        image: "/webp/temp66.webp",
        textBoxes: [
            { x: 50, y: 500, width: 600, height: 600, fontSize: 100, minFont: 40, align: "center" as const },
            { x: 710, y: 600, width: 600, height: 600, fontSize: 100, minFont: 40, align: "center" as const },
        ]
    },

    "they-dont-know": {
        image: "/webp/temp67.webp",
        textBoxes: [
            { x: 460, y: 300, width: 600, height: 400, fontSize: 70, minFont: 40, align: "center" as const },
        ]
    },

    "three-dragons": {
        image: "/webp/temp69.webp",
        textBoxes: [
            { x: 100, y: 300, width: 600, height: 600, fontSize: 150, minFont: 40, align: "center" as const },
            { x: 800, y: 400, width: 600, height: 600, fontSize: 150, minFont: 40, align: "center" as const },
            { x: 1500, y: 500, width: 600, height: 600, fontSize: 150, minFont: 40, align: "center" as const }
        ]
    },

    "understand-it": {
        image: "/webp/temp65.webp",
        textBoxes: [
            { x: 150, y: 200, width: 2700, height: 1500, fontSize: 200, minFont: 40, align: "center" as const },
            { x: 150, y: 2000, width: 2700, height: 400, fontSize: 200, minFont: 40, align: "center" as const },
        ]
    },

    "natures-beauty": {
        image: "/webp/temp64.webp",
        textBoxes: [
            { x: 1000, y: 1700, width: 800, height: 600, fontSize: 150, minFont: 40, align: "center" as const },
        ]
    },

    "sun-be": {
        image: "/webp/temp56.webp",
        textBoxes: [
            { x: 200, y: 250, width: 3000, height: 600, fontSize: 250, minFont: 40, align: "center" as const },
            { x: 200, y: 2325, width: 3000, height: 600, fontSize: 250, minFont: 40, align: "center" as const },
        ]
    },

    "i-fear-no-man": {
        image: "/webp/temp53.webp",
        textBoxes: [
            { x: 40, y: 1400, width: 1000, height: 600, fontSize: 200, minFont: 40, align: "center" as const },
        ]
    },

    "two-bros-handshake": {
        image: "/webp/temp52.webp",
        textBoxes: [
            { x: 10, y: 800, width: 1500, height: 600, fontSize: 250, minFont: 40, align: "center" as const },
            { x: 1700, y: 800, width: 1500, height: 600, fontSize: 250, minFont: 40, align: "center" as const },
            { x: 640, y: 2100, width: 2000, height: 800, fontSize: 250, minFont: 40, align: "center" as const },
        ]
    },

    "sleeping-on-cash": {
        image: "/webp/temp50.webp",
        textBoxes: [
            { x: 50, y: 200, width: 1800, height: 400, fontSize: 150, minFont: 40, align: "center" as const },
            { x: 0, y: 1025, width: 1900, height: 400, fontSize: 100, minFont: 40, align: "center" as const },
        ]
    },

    "Eh-i-step-on-shit": {
        image: "/webp/temp48.webp",
        textBoxes: [
            { x: 800, y: 2370, width: 600, height: 500, fontSize: 200, minFont: 40, align: "center" as const },
        ]
    },

    "space-shooting": {
        image: "/webp/temp45.webp",
        textBoxes: [
            { x: 200, y: 370, width: 600, height: 500, fontSize: 140, minFont: 40, align: "center" as const },
            { x: 700, y: 790, width: 600, height: 500, fontSize: 140, minFont: 40, align: "center" as const },
            { x: 1300, y: 750, width: 600, height: 500, fontSize: 140, minFont: 40, align: "center" as const }
        ]
    },

    "Rocky-superstar": {
        image: "/webp/temp41.webp",
        textBoxes: [
            { x: 0, y: 50, width: 365, height: 100, fontSize: 30, minFont: 10, align: "center" as const },
            { x: 0, y: 330, width: 365, height: 90, fontSize: 30, minFont: 10, align: "center" as const },
        ],
    },

    "Trade-offer": {
        image: "/webp/temp42.webp",
        textBoxes: [
            { x: 20, y: 220, width: 250, height: 200, fontSize: 30, minFont: 10, align: "center" as const },
            { x: 330, y: 220, width: 250, height: 200, fontSize: 30, minFont: 10, align: "center" as const },
        ],
    },

    "Water-UNO-Reverse": {
        image: "/webp/temp3.webp",
        textBoxes: [
            { x: 100, y: 200, width: 2000, height: 400, fontSize: 200, minFont: 40, align: "center" as const },
            { x: 100, y: 2200, width: 2000, height: 300, fontSize: 200, minFont: 40, align: "center" as const },
        ]
    },

    "Puzzle-Head": {
        image: "/webp/temp6.webp",
        textBoxes: [
            { id: "topText", x: 150, y: 400, width: 1100, height: 100, fontSize: 200, minFont: 60, align: "center" as const },
            { id: "handText", x: 910, y: 1000, width: 600, height: 100, fontSize: 200, minFont: 30, align: "center" as const },
        ],
    },

    "Trojan-Horse": {
        image: "/webp/temp4.webp",
        textBoxes: [
            { x: 2450, y: 2600, width: 900, height: 200, fontSize: 150, minFont: 40, align: "center" as const },
            { x: 2000, y: 2400, width: 900, height: 200, fontSize: 150, minFont: 40, align: "center" as const },
            { x: 590, y: 1900, width: 1000, height: 200, fontSize: 200, minFont: 40, align: "center" as const },
            { x: 1200, y: 1000, width: 800, height: 200, fontSize: 200, minFont: 40, align: "center" as const },
            { x: 2100, y: 400, width: 800, height: 200, fontSize: 300, minFont: 40, align: "center" as const }
        ]
    },

    "Ladies-Gang": {
        image: "/webp/temp15.webp",
        textBoxes: [
            { id: "topText", x: 0, y: 210, width: 200, height: 100, fontSize: 30, minFont: 8, align: "center" as const },
            { id: "handText", x: 90, y: 360, width: 200, height: 50, fontSize: 30, minFont: 8, align: "center" as const },
            { id: "handText", x: 330, y: 260, width: 200, height: 100, fontSize: 30, minFont: 8, align: "center" as const },
            { id: "handText", x: 510, y: 200, width: 200, height: 100, fontSize: 30, minFont: 8, align: "center" as const },
        ],
    },

    "empty-image": {
        image: "/webp/temp46.webp",
        textBoxes: [
            { x: 150, y: 270, width: 300, height: 200, fontSize: 60, minFont: 40, align: "center" as const }
        ]
    },

    "Cat-speaker": {
        image: "/webp/temp32.webp",
        textBoxes: [
            { x: 10, y: 30, width: 210, height: 100, fontSize: 20, minFont: 0, align: "center" as const },
            { x: 10, y: 200, width: 210, height: 100, fontSize: 20, minFont: 0, align: "center" as const },
        ],
    },

    "Megamind": {
        image: "/webp/temp39.webp",
        textBoxes: [
            { x: 30, y: 50, width: 500, height: 100, fontSize: 40, minFont: 10, align: "center" as const },
            { x: 30, y: 500, width: 500, height: 90, fontSize: 40, minFont: 10, align: "center" as const },
        ],
    },

    "Red-pill-Blue-pill": {
        image: "/webp/temp40.webp",
        textBoxes: [
            { x: 20, y: 365, width: 250, height: 200, fontSize: 30, minFont: 10, align: "center" as const },
            { x: 280, y: 365, width: 250, height: 200, fontSize: 30, minFont: 10, align: "center" as const },
        ],
    },

    "Bad-Horse": {
        image: "/webp/temp13.webp",
        textBoxes: [
            { id: "topText", x: 10, y: 140, width: 180, height: 200, fontSize: 40, minFont: 14, align: "center" as const },
            { id: "handText", x: 510, y: 140, width: 180, height: 200, fontSize: 40, minFont: 14, align: "center" as const },
        ],
    },

    "Butterfly-Man": {
        image: "/webp/temp12.webp",
        textBoxes: [
            { id: "topText", x: 430, y: 200, width: 280, height: 150, fontSize: 52, minFont: 20, align: "center" as const },
            { id: "handText", x: 0, y: 545, width: 400, height: 150, fontSize: 52, minFont: 14, align: "center" as const },
            { id: "handText2", x: 5, y: 700, width: 700, height: 60, fontSize: 100, minFont: 10, align: "center" as const },
        ],
    },

    "Kirat-Hunter-Eyes": {
        image: "/webp/temp17.webp",
        textBoxes: [
            { x: 2, y: 147, width: 130, height: 200, fontSize: 12, minFont: 10, align: "center" as const },
        ]
    },
};