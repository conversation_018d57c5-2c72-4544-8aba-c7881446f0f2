/**
 * 创建URL安全的slug
 * 将模板key转换为URL友好的格式，移除特殊字符
 */
export function createSafeSlug(templateKey: string): string {
  return templateKey
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // 移除特殊字符，只保留字母、数字、空格和连字符
    .replace(/\s+/g, '-') // 将空格替换为连字符
    .replace(/-+/g, '-') // 将多个连字符合并为一个
    .replace(/^-|-$/g, ''); // 移除开头和结尾的连字符
}

/**
 * 从slug查找对应的模板key
 */
export function findTemplateKeyBySlug(slug: string, templates: Record<string, unknown>): string | null {
  const templateKey = Object.keys(templates).find(
    key => createSafeSlug(key) === slug
  );
  
  return templateKey || null;
}

/**
 * 验证slug是否有效（非空且不包含特殊字符）
 */
export function isValidSlug(slug: string): boolean {
  return slug.length > 0 && /^[a-z0-9-]+$/.test(slug);
}
